package com.tecdo.galaxy.supplier.service.util;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tecdo.galaxy.supplier.domain.page.PageResp;

import java.util.List;
import java.util.function.Function;

/**
 * Date：2025/7/19 12:20
 * Description：分页工具类
 *
 * <AUTHOR>
 * @version v1.0.0
 */
public class PageUtil {

    /**
     * 将 IPage 转换为 PageResp<T>
     *
     * @param iPage     MyBatis Plus 分页结果
     * @param dtoClass  目标 DTO 类型
     * @param <T>       泛型类型
     * @return          统一的分页响应结构
     */
    public static <T> PageResp<T> convert(IPage<?> iPage, Class<T> dtoClass) {
        List<T> dtoList = BeanUtil.copyToList(iPage.getRecords(), dtoClass);
        return PageResp.of(
                (int) iPage.getCurrent(),
                (int) iPage.getSize(),
                iPage.getTotal(),
                dtoList
        );
    }

    /**
     * 带转换函数的转换方法（适用于复杂对象转换）
     *
     * @param iPage      MyBatis Plus 分页结果
     * @param converter  自定义转换函数
     * @param <E>        源数据类型（如 SupplierEvaluation）
     * @param <T>        目标 DTO 类型
     * @return           统一的分页响应结构
     */
    public static <E, T> PageResp<T> convert(IPage<E> iPage, Function<E, T> converter) {
        List<T> dtoList = iPage.getRecords().stream()
                .map(converter)
                .toList();
        return PageResp.of(
                (int) iPage.getCurrent(),
                (int) iPage.getSize(),
                iPage.getTotal(),
                dtoList
        );
    }
}
