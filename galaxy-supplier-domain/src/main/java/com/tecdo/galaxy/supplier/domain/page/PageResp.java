package com.tecdo.galaxy.supplier.domain.page;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * Date：2025/7/19 12:02
 * Description：分页出参
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class PageResp<T> {
    /**
     * 出参
     */
    private long total;

    /**
     * 数据列表
     */
    private List<T> list;

    /**
     * 页码
     */
    private int pageNum = 1;

    /**
     * 每页大小
     */
    private int pageSize = 10;

    /**
     * 构造方法（推荐使用 Builder 构建）
     */
    public PageResp(int pageNum, int pageSize, long total, List<T> list) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.list = list == null ? Collections.emptyList() : list;
    }

    /**
     * 构造空分页响应（total == 0 时使用）
     */
    public static <T> PageResp<T> empty() {
        return new PageResp<>(1, 10, 0, Collections.emptyList());
    }

    /**
     * 构造方法（用于快速构建，支持链式调用）
     */
    public static <T> PageResp<T> of(int pageNum, int pageSize, long total, List<T> list) {
        return new PageResp<>(pageNum, pageSize, total, list);
    }
}
