package com.tecdo.galaxy.supplier.web;

import com.tecdo.galaxy.supplier.api.SupplierRatingApi;
import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.rating.ListRatingQuery;
import com.tecdo.galaxy.supplier.domain.resp.rating.ListRatingVO;
import com.tecdo.galaxy.supplier.service.service.SupplierRatingService;
import com.tecdo.mac.mjga.common.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Date：2025/7/21 10:54
 * Description：供应商评级控制器
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@RestController
@Slf4j
@Validated
@RequestMapping("/supplier/rating")
public class SupplierRatingController implements SupplierRatingApi {

    @Resource
    private SupplierRatingService supplierRatingService;

    @Override
    public Result<PageResp<ListRatingVO>> listRatingPage(ListRatingQuery listRatingQuery) {
        return Result.success(supplierRatingService.listRatingPage(listRatingQuery));
    }
}
