package com.tecdo.galaxy.supplier.service.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tecdo.galaxy.supplier.dao.entity.SupplierEvaluationFactor;
import com.tecdo.galaxy.supplier.dao.mapper.SupplierEvaluationFactorMapper;
import com.tecdo.galaxy.supplier.domain.dto.tablequery.SupplierEvaluationFactorQuery;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Date：2025/7/21 17:37
 * Description：供应商评价要素repo
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Repository
public class SupplierEvaluationFactorRepository {

    @Resource
    private SupplierEvaluationFactorMapper supplierEvaluationFactorMapper;

    public int insert(SupplierEvaluationFactor entity) {
        return supplierEvaluationFactorMapper.insert(entity);
    }

    public int updateById(SupplierEvaluationFactor entity) {
        return supplierEvaluationFactorMapper.updateById(entity);
    }

    public IPage<SupplierEvaluationFactor> listPage(Page<SupplierEvaluationFactor> page,
        SupplierEvaluationFactorQuery supplierEvaluationFactorQuery) {
        return supplierEvaluationFactorMapper.selectPage(page, new LambdaQueryWrapper<>());
    }

    public List<SupplierEvaluationFactor> list(SupplierEvaluationFactorQuery supplierEvaluationFactorQuery) {
        return supplierEvaluationFactorMapper.selectList(new LambdaQueryWrapper<>());
    }

    public List<SupplierEvaluationFactor> listByIdLsit(List<Long> idList) {
        LambdaQueryWrapper<SupplierEvaluationFactor> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SupplierEvaluationFactor::getId, idList);
        return supplierEvaluationFactorMapper.selectList(wrapper);
    }
}
