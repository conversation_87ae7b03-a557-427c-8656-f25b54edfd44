package com.tecdo.galaxy.supplier.api;

import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.AddTypeDTO;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.BatchManagerSupplierTypeDTO;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.ListSupplierTypeQuery;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.UpdateTypeDTO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.type.SupplierTypeTreeVO;
import com.tecdo.mac.mjga.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @version V1.0.0
 * @Description: 供应商类型接口
 * @author: huan
 * @date: 2025/7/21 17:47
 * @Copyright:
 */
@Tag(name = "供应商类型相关接口", description = "供应商类型管理")
public interface SupplierTypeApi {

    /**
     * 新增供应商类型
     *
     * @param addTypeDTO
     * @return
     */
    @Operation(summary = "新增供应商类型")
    @PostMapping("/add")
    Result<Boolean> addType(@RequestBody @Valid AddTypeDTO addTypeDTO);

    /**
     * 修改供应商类型
     *
     * @param updateTypeDTO
     * @return
     */
    @Operation(summary = "修改供应商类型")
    @PostMapping("/update")
    Result<Boolean> updateType(@RequestBody @Valid UpdateTypeDTO updateTypeDTO);

    /**
     * 查询供应商类型树
     * @param listSupplierTypeQuery
     * @return
     */
    @Operation(summary = "查询供应商类型树")
    @GetMapping("/list-tree")
    Result<List<SupplierTypeTreeVO>> listTree(@ParameterObject ListSupplierTypeQuery listSupplierTypeQuery);
    /**
     * 批量管理供应商类型
     *
     * @param batchManagerSupplierTypeDTO
     * @return
     */
    @Operation(summary = "批量管理供应商类型")
    @PostMapping("/batch-manager-supplier-type")
    Result<Boolean> batchManagerSupplierType(
        @RequestBody @Valid BatchManagerSupplierTypeDTO batchManagerSupplierTypeDTO);



}
