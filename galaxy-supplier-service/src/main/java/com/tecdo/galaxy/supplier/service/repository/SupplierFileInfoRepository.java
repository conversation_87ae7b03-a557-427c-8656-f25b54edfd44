package com.tecdo.galaxy.supplier.service.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tecdo.galaxy.supplier.dao.entity.SupplierFileInfo;
import com.tecdo.galaxy.supplier.dao.mapper.SupplierFileInfoMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * Date：2025/7/9 18:06
 * Description：供应商文件信息Repository
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Repository
public class SupplierFileInfoRepository {

    @Resource
    private SupplierFileInfoMapper supplierFileInfoMapper;

    public int batchInsert(List<SupplierFileInfo> list) {
        return supplierFileInfoMapper.batchInsert(list);
    }

    public int deleteByBizTypeAndBizId(String bizType, String bizId) {
        LambdaQueryWrapper<SupplierFileInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierFileInfo::getBizType, bizType)
                .eq(SupplierFileInfo::getBizId, bizId);
        return supplierFileInfoMapper.delete(queryWrapper);
    }

    public List<SupplierFileInfo> getByBizTypeAndBizId(String bizType, String bizId) {
        LambdaQueryWrapper<SupplierFileInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierFileInfo::getBizType, bizType)
                .eq(SupplierFileInfo::getBizId, bizId);
        return supplierFileInfoMapper.selectList(queryWrapper);
    }

}
