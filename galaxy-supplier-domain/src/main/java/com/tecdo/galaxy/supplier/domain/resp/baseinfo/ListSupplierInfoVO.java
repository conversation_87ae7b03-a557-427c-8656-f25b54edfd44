package com.tecdo.galaxy.supplier.domain.resp.baseinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Date：2025/7/16 20:42
 * Description：查询供应商信息列表响应体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class ListSupplierInfoVO {

    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "供应商名称", example = "北京科技有限公司")
    private String name;

    @Schema(description = "合作年限,距供应商入库时间", example = "4")
    private Integer cooperationYear;

    @Schema(description = "合作次数", example = "5")
    private Integer cooperationCount;

    @Schema(description = "风险预警次数", example = "0")
    private Integer riskCount;

    @Schema(description = "最近评分", example = "95.5")
    private BigDecimal score;

    @Schema(description = "当前评级", example = "A")
    private String currentRating;


}
