package com.tecdo.galaxy.supplier.service.service;

import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.AddSupplierInfoDTO;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.ListSupplierInfoQuery;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.UpdateSupplierInfoDTO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.GetSupplierInfoVO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.ListSupplierInfoVO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.WildcardQueryNameAndCodeListByNameVO;

import java.util.List;

/**
 * Date：2025/7/7 19:27
 * Description：供应商信息服务
 *
 * <AUTHOR>
 * @version v1.0.0
 */
public interface SupplierInfoService {

    /**
     * 新增供应商信息
     * @param req
     * @return
     */
    boolean addSupplierInfo(AddSupplierInfoDTO req);

    /**
     * 修改供应商信息
     * @param req
     * @return
     */
    boolean updateSupplierInfo(UpdateSupplierInfoDTO req);

    /**
     * 查询供应商信息
     * @param req
     * @return
     */
    PageResp<ListSupplierInfoVO> listSupplierInfo(ListSupplierInfoQuery req);

    /**
     * 查询供应商详情
     * @param id
     * @return
     */
    GetSupplierInfoVO getSupplierInfo(long id);

    /**
     * 根据供应商名称模糊查询供应商名称及编码
     * @param name
     * @return
     */
    List<WildcardQueryNameAndCodeListByNameVO> wildcardQueryNameAndCodeListByName(String name);

}
