package com.tecdo.galaxy.supplier.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tecdo.galaxy.supplier.dao.entity.SupplierFileInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 供应商模块相关文件表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Mapper
public interface SupplierFileInfoMapper extends BaseMapper<SupplierFileInfo> {

    /**
     * 批量插入供应商文件信息
     *
     * @param list 供应商文件信息列表
     * @return 插入成功的记录数
     */
    int batchInsert(List<SupplierFileInfo> list);
}
