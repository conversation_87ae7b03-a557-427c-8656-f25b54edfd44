<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tecdo.galaxy</groupId>
        <artifactId>galaxy-supplier</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>start</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.tecdo.mac</groupId>
            <artifactId>mjga-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tecdo.mac</groupId>
            <artifactId>mjga-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tecdo.galaxy</groupId>
            <artifactId>galaxy-supplier-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tecdo.galaxy</groupId>
            <artifactId>galaxy-supplier-web</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.parent.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <!-- 由于layer分层在springboot 2.4版本以后默认启用，所以这里不需要设置
                    <layers>
                        <enabled>true</enabled>
                    </layers> -->
                    <outputDirectory>${project.parent.basedir}/target</outputDirectory>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
