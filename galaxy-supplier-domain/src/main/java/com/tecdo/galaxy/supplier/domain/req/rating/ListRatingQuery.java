package com.tecdo.galaxy.supplier.domain.req.rating;

import com.tecdo.galaxy.supplier.domain.page.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Date：2025/7/21 11:59
 * Description：查询评价列表请求体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class ListRatingQuery extends PageReq {

    @Schema(description = "供应商编码", example = "123456", required = true)
    private String supplierCode;

    @Schema(description = "评级", example = "5", required = true)
    private String rating;

    @Schema(description = "评级更新时间左区间", example = "2025-07-21 11:59:00", required = true)
    private String ratingUpdateTimeStart;

    @Schema(description = "评级更新时间右区间", example = "2025-07-21 11:59:00", required = true)
    private String ratingUpdateTimeEnd;


}
