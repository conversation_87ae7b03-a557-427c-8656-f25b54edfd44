package com.tecdo.galaxy.supplier.domain.req.baseinfo;

import com.tecdo.galaxy.supplier.domain.req.FileInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Date：2025/7/7 18:00
 * Description：更新供应商信息请求体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
@Schema(description = "更新供应商信息请求体")
public class UpdateSupplierInfoDTO {

    @Schema(description = "供应商ID", example = "1", required = true)
    @NotNull(message = "供应商id不能为空")
    private Long id;

    @Schema(description = "供应商名称", example = "北京科技有限公司")
    private String name;

    @Schema(description = "统一社会信用代码", example = "91110000123456789X")
    private String creditCode;

    @Schema(description = "注册地址", example = "北京市朝阳区xxx街道xxx号")
    private String registerAddress;

    @Schema(description = "法定代表人", example = "张三")
    private String legalPerson;

    @Schema(description = "主营业务", example = "软件开发、技术咨询")
    private String mainBusiness;

    @Schema(description = "注册资本(万元)", example = "1000.00")
    @Min(value = 0, message = "注册资本不能小于0")
    private BigDecimal registeredCapital;

    @Schema(description = "营业开始日期", example = "2020-01-01")
    private LocalDate businessStartDate;

    @Schema(description = "营业期限", example = "长期")
    private String businessTerm;

    @Schema(description = "供应商类型", example = "生产型", allowableValues = {"生产型", "贸易型", "服务型", "其他"})
    private String supplierType;

    @Schema(description = "供应商联系人姓名", example = "李四")
    private String contactName;

    @Schema(description = "供应商联系人电话", example = "***********")
    private String contactPhone;

    @Schema(description = "供应商联系人邮箱", example = "<EMAIL>")
    private String contactEmail;

    @Schema(description = "开户行", example = "中国工商银行北京分行")
    private String bankName;

    @Schema(description = "银行账号", example = "6222021234567890123")
    private String bankAccountNo;

    @Schema(description = "账户户名", example = "北京科技有限公司")
    private String bankAccountName;

    @Schema(description = "更新人", example = "admin")
    private String updateBy;

    @Schema(description = "文件列表")
    private List<FileInfoDTO> fileInfoList;

}
