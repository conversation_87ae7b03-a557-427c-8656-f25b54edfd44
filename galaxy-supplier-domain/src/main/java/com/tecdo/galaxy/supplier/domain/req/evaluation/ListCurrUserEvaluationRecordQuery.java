package com.tecdo.galaxy.supplier.domain.req.evaluation;

import com.tecdo.galaxy.supplier.domain.page.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Date：2025/7/19 16:17
 * Description: 查询供应商评分记录列表请求体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class ListCurrUserEvaluationRecordQuery extends PageReq {

    @Schema(description = "供应商编号")
    private String supplierCode;

    @Schema(description = "采购单验收时间")
    private LocalDateTime purchaseAcceptanceTimeStart;

    @Schema(description = "采购单验收时间")
    private LocalDateTime purchaseAcceptanceTimeEnd;

    @Schema(description = "评分状态：INIT待评分FINISH完成评分")
    private String evaluationStatus;

}
