package com.tecdo.galaxy.supplier.web;

import com.tecdo.galaxy.supplier.api.SupplierTypeEvaluationRelationApi;
import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation.AddRelationDTO;
import com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation.ListTypeEvaluationRelationQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation.UpdateRelationDTO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.tyepefactorrelation.ListTypeEvaluationRelationVO;
import com.tecdo.galaxy.supplier.service.service.SupplierTypeEvaluationRelationService;
import com.tecdo.mac.mjga.common.response.Result;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Date：2025/7/21 20:01
 * Description：供应商类型与评估要素相关接口，用于处理供应商类型和评估项之间的关联逻辑
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@RestController
@Slf4j
@Validated
@RequestMapping("/supplier/type-evaluation-relation")
public class SupplierTypeEvaluationRelationController implements SupplierTypeEvaluationRelationApi {


    @Resource
    private SupplierTypeEvaluationRelationService supplierTypeEvaluationRelationService;

    @Override
    public Result<PageResp<ListTypeEvaluationRelationVO>> listPage(
        ListTypeEvaluationRelationQuery listTypeEvaluationRelationQuery) {
        return Result.success(supplierTypeEvaluationRelationService.listPage(listTypeEvaluationRelationQuery));
    }

    @Override
    public Result<Boolean> addRelation(AddRelationDTO addRelationDTO) {
        return Result.success(supplierTypeEvaluationRelationService.addRelation(addRelationDTO));
    }

    @Override
    public Result<Boolean> updateRelation(UpdateRelationDTO addRelationDTO) {
        return Result.success(supplierTypeEvaluationRelationService.updateRelation(addRelationDTO));
    }
}
