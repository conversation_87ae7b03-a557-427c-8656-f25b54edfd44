package com.tecdo.galaxy.supplier.client;

import com.tecdo.galaxy.supplier.api.SupplierTypeApi;
import com.tecdo.mac.mjga.web.feign.annotation.FeignService;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @version V1.0.0
 * @Description: 供应商类型feign服务
 * @author: huan
 * @date: 2025/7/21 17:47
 * @Copyright:
 */
@FeignService
@RequestMapping("/supplier/type")
public interface SupplierTypeFeignService extends SupplierTypeApi {

}
