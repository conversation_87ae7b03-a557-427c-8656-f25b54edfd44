package com.tecdo.galaxy.supplier.service.mq.consumer;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketMQMessageListener(
        topic = "MESSAGE_TASK_TOPIC",
        consumerGroup = "${rocketmq.consumer.group}",
        maxReconsumeTimes = 3
)
public class MessageTaskConsumer implements RocketMQListener<MessageExt> {

    @Value("${rocketmq.consumer.group}")
    private String consumerGroup;

    @Override
    public void onMessage(MessageExt  message) {

    }
}