package com.tecdo.galaxy.supplier.api;

import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.AddSupplierInfoDTO;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.ListSupplierInfoQuery;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.UpdateSupplierInfoDTO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.GetSupplierInfoVO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.ListSupplierInfoVO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.WildcardQueryNameAndCodeListByNameVO;
import com.tecdo.mac.mjga.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @version V1.0.0
 * @Description: 供应商信息接口
 * @author: huan
 * @date: 2025/7/7 18:08
 * @Copyright:
 */
@Tag(name = "供应商基础信息管理相关接口", description = "供应商信息管理")
public interface SupplierInfoApi {

    /**
     * 新增供应商信息
     *
     * @param addSupplierInfoDTO
     * @return
     */
    @Operation(summary = "新增供应商信息")
    @PostMapping("/add")
    Result<Boolean> addSupplierInfo(@RequestBody @Valid AddSupplierInfoDTO addSupplierInfoDTO);

    /**
     * 修改供应商信息
     *
     * @param updateSupplierInfoDTO
     * @return
     */
    @Operation(summary = "修改供应商信息")
    @PostMapping("/update")
    Result<Boolean> updateSupplierInfo(@RequestBody @Valid UpdateSupplierInfoDTO updateSupplierInfoDTO);

    /**
     * 查询供应商信息
     *
     * @param listSupplierInfoQuery
     * @return
     */
    @Operation(summary = "查询供应商信息列表")
    @PostMapping("/list-page")
    Result<PageResp<ListSupplierInfoVO>> listSupplierInfoPage(
        @ParameterObject ListSupplierInfoQuery listSupplierInfoQuery);

    /**
     * 查询供应商详情
     *
     * @param id
     * @return
     */
    @Operation(summary = "查询供应商详情信息查询")
    @GetMapping("/detail")
    Result<GetSupplierInfoVO> getSupplierInfoDetail(@RequestParam("id") long id);

    /**
     * 根据名称模糊查询供应商名称及编码
     *
     * @param name
     * @return
     */
    @Operation(summary = "根据供应商名称模糊查询供应商名称及编码")
    @GetMapping("/wildcard-query-list-by-name")
    Result<List<WildcardQueryNameAndCodeListByNameVO>> wildcardQueryNameAndCodeListByName(
        @RequestParam("name") String name);

}
