package com.tecdo.galaxy.supplier.web;

import com.tecdo.galaxy.supplier.api.SupplierRiskApi;
import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.risk.ListRiskQuery;
import com.tecdo.galaxy.supplier.domain.resp.risk.ListRiskVO;
import com.tecdo.galaxy.supplier.domain.resp.risk.RiskDetailResp;
import com.tecdo.galaxy.supplier.service.service.SupplierRiskInfoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Date：2025/7/23 18:31
 * Description:供应商风险控制器
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@RestController
@Slf4j
@Validated
@RequestMapping("/supplier/risk")
public class SupplierRiskController implements SupplierRiskApi {

    @Resource
    private SupplierRiskInfoService supplierRiskInfoService;

    @Override
    public PageResp<ListRiskVO> listRiskPage(ListRiskQuery listRiskQuery) {
        return supplierRiskInfoService.listRisk(listRiskQuery);
    }

    @Override
    public RiskDetailResp detail(String supplierCode) {
        return supplierRiskInfoService.detail(supplierCode);
    }
}
