package com.tecdo.galaxy.supplier.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tecdo.galaxy.supplier.dao.entity.SupplierEvaluationFactor;
import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.evaluation.factor.AddEvaluationFactorDTO;
import com.tecdo.galaxy.supplier.domain.req.evaluation.factor.ListEvaluationFactorQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.factor.UpdateEvaluationFactorDTO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.factor.ListEvaluationFactorVO;
import com.tecdo.galaxy.supplier.service.repository.SupplierEvaluationFactorRepository;
import com.tecdo.galaxy.supplier.service.service.SupplierEvaluationFactorService;
import com.tecdo.galaxy.supplier.service.util.PageUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Date：2025/7/21 17:36 Description：供应商评价要素服务实现类
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Service
public class SupplierEvaluationFactorServiceImpl implements SupplierEvaluationFactorService {

    @Resource
    private SupplierEvaluationFactorRepository supplierEvaluationFactorRepository;

    @Override
    public boolean addType(AddEvaluationFactorDTO addEvaluationFactorDTO) {
        SupplierEvaluationFactor entity =
            BeanUtil.copyProperties(addEvaluationFactorDTO, SupplierEvaluationFactor.class);
        return supplierEvaluationFactorRepository.insert(entity) > 0;
    }

    @Override
    public boolean updateType(UpdateEvaluationFactorDTO updateEvaluationFactorDTO) {
        SupplierEvaluationFactor entity =
            BeanUtil.copyProperties(updateEvaluationFactorDTO, SupplierEvaluationFactor.class);
        return supplierEvaluationFactorRepository.updateById(entity) > 0;
    }

    @Override
    public PageResp<ListEvaluationFactorVO> listPage(ListEvaluationFactorQuery listEvaluationFactorQuery) {
        IPage<SupplierEvaluationFactor> pageResult = supplierEvaluationFactorRepository.listPage(
            Page.of(listEvaluationFactorQuery.getPageNum(), listEvaluationFactorQuery.getPageSize()), null);
        return PageUtil.convert(pageResult, o -> BeanUtil.copyProperties(o, ListEvaluationFactorVO.class));
    }

    @Override
    public List<ListEvaluationFactorVO> list(ListEvaluationFactorQuery listEvaluationFactorQuery) {
        return BeanUtil.copyToList(supplierEvaluationFactorRepository.list(null), ListEvaluationFactorVO.class);
    }
}
