package com.tecdo.galaxy.supplier.domain.resp.rating;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Date：2025/7/21 11:59
 * Description：查询评价列表返回体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class ListRatingVO {

    @Schema(description = "供应商名称", example = "北京科技有限公司", required = true)
    private String name;

    @Schema(description = "供应商编码", example = "123456", required = true)
    private String supplierCode;

    @Schema(description = "评级", example = "5", required = true)
    private String rating;

    @Schema(description = "评级更新时间", example = "2025-07-21 11:59:00", required = true)
    private String ratingUpdateTime;

    @Schema(description = "评分", example = "4.0", required = true)
    private String score;

}
