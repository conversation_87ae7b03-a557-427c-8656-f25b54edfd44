package com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation;

import com.tecdo.galaxy.supplier.domain.page.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Date：2025/7/21 19:48
 * Description：查询供应商类型和评价要素关联关系列表请求参数
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class ListTypeEvaluationRelationQuery extends PageReq {

    @Schema(description = "供应商类型名称")
    private String typeName;

}
