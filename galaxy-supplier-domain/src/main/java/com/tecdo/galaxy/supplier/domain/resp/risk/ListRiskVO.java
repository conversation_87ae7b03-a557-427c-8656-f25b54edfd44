package com.tecdo.galaxy.supplier.domain.resp.risk;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Date：2025/7/23 17:39
 * Description：查询供应商风险列表请求体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class ListRiskVO {

    @Schema(description = "供应商名称")
    private String name;

    @Schema(description = "供应商编码")
    private String supplierCode;

    @Schema(description = "合作次数")
    private String cooperationCount;

    @Schema(description = "评级")
    private String rating;

    @Schema(description = "评分")
    private String score;

    @Schema(description = "风险次数")
    private String riskCount;

}
