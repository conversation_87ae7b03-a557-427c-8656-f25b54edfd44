package com.tecdo.galaxy.supplier.domain.resp.evaluation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Date：2025/7/19 16:11
 * Description：查询供应商评分记录响应体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class ListCurrUserEvaluationRecordVO {

    @Schema(description = "采购单号")
    private String purchaseId;

    @Schema(description = "采购计划单号")
    private String purchasePlanId;

    @Schema(description = "供应商编号")
    private String supplierCode;

    @Schema(description = "采购单验收时间")
    private LocalDateTime purchaseAcceptanceTime;

    @Schema(description = "评分状态：INIT待评分FINISH完成评分")
    private String evaluationStatus;


}
