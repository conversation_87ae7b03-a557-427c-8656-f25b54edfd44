package com.tecdo.galaxy.supplier.domain.req.baseinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tecdo.galaxy.supplier.domain.req.FileInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Date：2025/7/7 18:00
 * Description：新增供应商信息请求体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
@Schema(description = "新增供应商信息请求体")
public class AddSupplierInfoDTO {

    @Schema(description = "供应商名称", example = "北京科技有限公司", required = true)
    @NotBlank(message = "供应商名称不能为空")
    private String name;

    @Schema(description = "统一社会信用代码", example = "91110000123456789X", required = true)
    @NotBlank(message = "统一社会信用代码不能为空")
    private String creditCode;

    @Schema(description = "注册地址", example = "北京市朝阳区xxx街道xxx号", required = true)
    @NotBlank(message = "注册地址不能为空")
    private String registerAddress;

    @Schema(description = "法定代表人", example = "张三", required = true)
    @NotBlank(message = "法定代表人不能为空")
    private String legalPerson;

    @Schema(description = "主营业务", example = "软件开发、技术咨询", required = true)
    @NotBlank(message = "主营业务不能为空")
    private String mainBusiness;

    @Schema(description = "注册资本(万元)", example = "1000.00", required = true)
    @NotNull(message = "注册资本不能为空")
    @Positive(message = "注册资本不能小于0")
    private BigDecimal registeredCapital;

    @Schema(description = "营业开始日期", example = "2020-01-01", required = true)
    @NotNull(message = "营业开始日期不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate businessStartDate;

    @Schema(description = "营业期限", example = "长期", required = true)
    @NotBlank(message = "营业期限不能为空")
    private String businessTerm;

    @Schema(description = "供应商类型", example = "生产型", required = true,
            allowableValues = {"生产型", "贸易型", "服务型", "其他"})
    @NotBlank(message = "供应商类型不能为空")
    private String supplierType;

    @Schema(description = "供应商联系人姓名", example = "李四", required = true)
    @NotBlank(message = "供应商联系人姓名不能为空")
    private String contactName;

    @Schema(description = "供应商联系人电话", example = "***********", required = true)
    @NotBlank(message = "供应商联系人电话不能为空")
    private String contactPhone;

    @Schema(description = "供应商联系人邮箱", example = "<EMAIL>", required = true)
    @NotBlank(message = "供应商联系人邮箱不能为空")
    private String contactEmail;

    @Schema(description = "开户行", example = "中国工商银行北京分行", required = true)
    @NotBlank(message = "开户行不能为空")
    private String bankName;

    @Schema(description = "银行账号", example = "6222021234567890123", required = true)
    @NotBlank(message = "银行账号不能为空")
    private String bankAccountNo;

    @Schema(description = "账户户名", example = "北京科技有限公司", required = true)
    @NotBlank(message = "账户户名不能为空")
    private String bankAccountName;

    @Schema(description = "创建人", example = "1", required = true)
    @Positive(message = "创建人不能为空")
    private Long createBy;

    @Valid
    @Schema(description = "文件地址列表")
    private List<FileInfoDTO> fileInfoList;

}
