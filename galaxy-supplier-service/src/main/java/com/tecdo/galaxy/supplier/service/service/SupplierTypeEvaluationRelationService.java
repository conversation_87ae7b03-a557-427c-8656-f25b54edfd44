package com.tecdo.galaxy.supplier.service.service;

import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation.AddRelationDTO;
import com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation.ListTypeEvaluationRelationQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation.UpdateRelationDTO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.tyepefactorrelation.ListTypeEvaluationRelationVO;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Date：2025/7/21 20:17 Description：供应商类型和评价要素关联关系服务
 *
 * <AUTHOR>
 * @version v1.0.0
 */
public interface SupplierTypeEvaluationRelationService {

    /**
     * 查询供应商类型和评价要素关联关系列表
     *
     * @param listTypeEvaluationRelationQuery 查询参数
     * @return 供应商类型和评价要素关联关系列表
     */
    PageResp<ListTypeEvaluationRelationVO> listPage(ListTypeEvaluationRelationQuery listTypeEvaluationRelationQuery);

    /**
     * 添加关联关系
     *
     * @param addRelationDTO
     * @return
     */
    boolean addRelation(@RequestBody AddRelationDTO addRelationDTO);

    /**
     * 添加关联关系
     *
     * @param updateRelationDTO
     * @return
     */
    boolean updateRelation(@RequestBody UpdateRelationDTO updateRelationDTO);

}
