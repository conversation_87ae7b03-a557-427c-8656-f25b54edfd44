package com.tecdo.galaxy.supplier.service.service;

import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.risk.ListRiskQuery;
import com.tecdo.galaxy.supplier.domain.resp.risk.ListRiskVO;
import com.tecdo.galaxy.supplier.domain.resp.risk.RiskDetailResp;

/**
 * Date：2025/7/23 21:15
 * Description：供应商风险服务
 *
 * <AUTHOR>
 * @version v1.0.0
 */
public interface SupplierRiskInfoService {

    PageResp<ListRiskVO> listRisk(ListRiskQuery req);

    RiskDetailResp detail(String supplierCode);
}
