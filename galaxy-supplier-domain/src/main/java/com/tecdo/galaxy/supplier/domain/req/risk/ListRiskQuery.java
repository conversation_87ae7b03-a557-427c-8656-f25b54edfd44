package com.tecdo.galaxy.supplier.domain.req.risk;

import com.tecdo.galaxy.supplier.domain.page.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Date：2025/7/23 17:39
 * Description：查询供应商风险列表请求体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class ListRiskQuery extends PageReq {

    @Schema(description = "供应商编码")
    private String supplierCode;

    @Schema(description = "评级")
    private String rating;

    @Schema(description = "最低分")
    private BigDecimal minScore;

    @Schema(description = "最高分")
    private BigDecimal maxScore;

    @Schema(description = "最少合作次数")
    private String minCooperationCount;

    @Schema(description = "最多合作次数")
    private String maxCooperationCount;


}
