package com.tecdo.galaxy.supplier.api;

import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.rating.ListRatingQuery;
import com.tecdo.galaxy.supplier.domain.resp.rating.ListRatingVO;
import com.tecdo.mac.mjga.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * Date：2025/7/21 10:55
 * Description：供应商评级接口
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Tag(name = "供应商评级相关接口", description = "供应商评级管理")
public interface SupplierRatingApi {

    /**
     * 查询供应商评级列表
     * @param listRatingQuery
     * @return
     */
    @Operation(summary = "查询供应商评级列表")
    @PostMapping("/list-page")
    Result<PageResp<ListRatingVO>> listRatingPage(@ParameterObject ListRatingQuery listRatingQuery);

}
