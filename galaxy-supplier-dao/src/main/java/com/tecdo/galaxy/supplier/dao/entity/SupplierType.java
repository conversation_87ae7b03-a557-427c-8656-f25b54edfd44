package com.tecdo.galaxy.supplier.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 供应商类型表（三级业务分类）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Getter
@Setter
@ToString
@TableName("supplier_type")
@Schema(name = "SupplierType", description = "供应商类型表（三级业务分类）")
public class SupplierType implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 供应商类型ID
     */
    @Schema(description = "供应商类型ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父分类ID，顶级分类为0
     */
    @TableField("parent_id")
    @Schema(description = "父分类ID，顶级分类为0")
    private Long parentId;

    /**
     * 供应商类型名称
     */
    @TableField("name")
    @Schema(description = "供应商类型名称")
    private String name;

    /**
     * 供应商类型描述
     */
    @TableField("description")
    @Schema(description = "供应商类型描述")
    private String description;

    /**
     * 分类路径，格式如:1/2/3
     */
    @TableField("path")
    @Schema(description = "分类路径，格式如:1/2/3")
    private String path;

    /**
     * 分类层级
     */
    @TableField("level")
    @Schema(description = "分类层级")
    private Integer level;

    /**
     * 创建人id
     */
    @TableField("create_by")
    @Schema(description = "创建人id")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人id
     */
    @TableField("update_by")
    @Schema(description = "更新人id")
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    @Schema(description = "租户id")
    private String tenantId;

    /**
     * 状态：正常：VALID,失效：INVALID
     */
    @TableField("status")
    @Schema(description = "状态：正常：VALID,失效：INVALID")
    private String status;
}
