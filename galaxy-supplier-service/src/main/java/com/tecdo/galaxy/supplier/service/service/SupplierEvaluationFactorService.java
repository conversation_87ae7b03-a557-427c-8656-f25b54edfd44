package com.tecdo.galaxy.supplier.service.service;

import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.evaluation.factor.AddEvaluationFactorDTO;
import com.tecdo.galaxy.supplier.domain.req.evaluation.factor.ListEvaluationFactorQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.factor.UpdateEvaluationFactorDTO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.factor.ListEvaluationFactorVO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @version V1.0.0
 * @Description: 供应商评价要素服务
 * @author: huan
 * @date: 2025/7/21 17:36
 * @Copyright:
 */
public interface SupplierEvaluationFactorService {

    /**
     * 新增供应商信息
     *
     * @param addEvaluationFactorDTO
     * @return
     */
    boolean addType(@RequestBody AddEvaluationFactorDTO addEvaluationFactorDTO);

    /**
     * 修改供应商信息
     *
     * @param updateEvaluationFactorDTO
     * @return
     */
    boolean updateType(@RequestBody UpdateEvaluationFactorDTO updateEvaluationFactorDTO);

    /**
     * 查询供应商评价要素列表-分页
     * @return
     */
    PageResp<ListEvaluationFactorVO> listPage(ListEvaluationFactorQuery listEvaluationFactorQuery);

    /**
     * 查询供应商评价要素列表
     *
     * @return
     */
    List<ListEvaluationFactorVO> list(ListEvaluationFactorQuery listEvaluationFactorQuery);
}
