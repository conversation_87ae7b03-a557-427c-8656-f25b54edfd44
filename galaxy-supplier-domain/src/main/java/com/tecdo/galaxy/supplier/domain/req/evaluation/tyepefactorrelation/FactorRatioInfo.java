package com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * Date：2025/7/27 20:32 Description：评价因素占比信息
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class FactorRatioInfo {

    @NotNull(message = "评价要素id不能为空")
    @Min(value = 1, message = "评价要素id不能小于1")
    @Schema(description = "评价要素id")
    private Long factorId;

    @NotNull(message = "评价要素权重不能为空")
    @Min(value = 1, message = "权重不能小于1")
    @Max(value = 100, message = "权重不能大于100")
    @Schema(description = "评价要素权重")
    private int weight;
}
