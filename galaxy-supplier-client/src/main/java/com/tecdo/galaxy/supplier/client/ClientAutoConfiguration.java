package com.tecdo.galaxy.supplier.client;

import com.tecdo.mac.mjga.web.feign.AppFeignClientConfig;
import com.tecdo.mac.mjga.web.feign.FeignServiceRegistryPostProcessor;
import com.tecdo.mac.mjga.web.feign.config.FeignAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Feign服务自动配置类
 * 用于自动启用FeignService功能
 */
@Slf4j
@Configuration
@AutoConfigureAfter(FeignAutoConfiguration.class)
public class ClientAutoConfiguration {

    @Bean(Constants.APP_NAME + "FeignServiceRegistryPostProcessor")
    public FeignServiceRegistryPostProcessor processor() {
        log.info("init ClientAutoConfiguration for {}", Constants.APP_NAME);

        AppFeignClientConfig config = AppFeignClientConfig.builder()
                .scanPackages(new String[]{this.getClass().getPackageName()})
                .clientName(Constants.APP_NAME)
                .contextPath(Constants.CONTEXT_PATH)
                .build();

        return new FeignServiceRegistryPostProcessor(config);
    }

}
