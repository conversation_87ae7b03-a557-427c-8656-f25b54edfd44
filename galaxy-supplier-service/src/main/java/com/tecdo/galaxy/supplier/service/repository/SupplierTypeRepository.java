package com.tecdo.galaxy.supplier.service.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tecdo.galaxy.supplier.dao.entity.SupplierType;
import com.tecdo.galaxy.supplier.dao.mapper.SupplierTypeMapper;
import com.tecdo.galaxy.supplier.domain.dto.tablequery.SupplierTypeQuery;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * Date：2025/7/21 18:06
 * Description：供应商类型repo
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Repository
public class SupplierTypeRepository {

    @Resource
    private SupplierTypeMapper supplierTypeMapper;

    public boolean insert(SupplierType entity) {
        return supplierTypeMapper.insert(entity) > 0;
    }

    public boolean updateById(SupplierType entity) {
        return supplierTypeMapper.updateById(entity) > 0;
    }

    public List<SupplierType> list(SupplierTypeQuery req) {
        LambdaQueryWrapper<SupplierType> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(req.getName()), SupplierType::getName, req.getName());
        wrapper.orderByAsc(SupplierType::getParentId, SupplierType::getId);
        return supplierTypeMapper.selectList(wrapper);
    }
}