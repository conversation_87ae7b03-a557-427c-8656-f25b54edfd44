package com.tecdo.galaxy.supplier.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 供应商评价记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Getter
@Setter
@ToString
@TableName("supplier_evaluation_record")
@Schema(name = "SupplierEvaluationRecord", description = "供应商评价记录表")
public class SupplierEvaluationRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 采购单号
     */
    @TableField("purchase_id")
    @Schema(description = "采购单号")
    private String purchaseId;

    /**
     * 采购计划单号
     */
    @Schema(description = "采购计划单号")
    @TableField("purchase_plan_id")
    private String purchasePlanId;

    /**
     * 供应商编号
     */
    @TableField("supplier_code")
    @Schema(description = "供应商编号")
    private String supplierCode;

    /**
     * 采购单验收时间
     */
    @Schema(description = "采购单验收时间")
    @TableField("purchase_acceptance_time")
    private LocalDateTime purchaseAcceptanceTime;

    /**
     * 交付速度分
     */
    @Schema(description = "交付速度分")
    @TableField("delivery_speed_score")
    private BigDecimal deliverySpeedScore;

    /**
     * 质量合格率
     */
    @Schema(description = "质量合格率")
    @TableField("quality_pass_score")
    private BigDecimal qualityPassScore;

    /**
     * 服务响应速度得分
     */
    @Schema(description = "服务响应速度得分")
    @TableField("service_response_score")
    private BigDecimal serviceResponseScore;

    /**
     * 价格竞争力得分
     */
    @TableField("price_score")
    @Schema(description = "价格竞争力得分")
    private BigDecimal priceScore;

    /**
     * 总分
     */
    @TableField("score")
    @Schema(description = "总分")
    private BigDecimal score;

    /**
     * 评分内容
     */
    @Schema(description = "评分内容")
    @TableField("evaluation_content")
    private String evaluationContent;

    /**
     * 评分状态：INIT待评分FINISH完成评分
     */
    @TableField("evaluation_status")
    @Schema(description = "评分状态：INIT待评分FINISH完成评分")
    private String evaluationStatus;

    /**
     * 创建人id
     */
    @TableField("create_by")
    @Schema(description = "创建人id")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人id
     */
    @TableField("update_by")
    @Schema(description = "更新人id")
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 评分时间
     */
    @Schema(description = "评分时间")
    @TableField("evaluation_time")
    private LocalDateTime evaluationTime;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    @Schema(description = "租户id")
    private String tenantId;
}
