package com.tecdo.galaxy.supplier.domain.req.evaluation.factor;

import com.tecdo.galaxy.supplier.domain.validation.NullableNotBlank;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * Date：2025/7/27 19:23 Description：更新评价要素请求体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class UpdateEvaluationFactorDTO {

    @Min(value = 1, message = "评价要素id不能小于1")
    @Schema(description = "评价要素id")
    private Long id;

    @NullableNotBlank(message = "评价要素名称不能为空字符串")
    @Schema(description = "评价要素名称")
    private String factorName;

    @Schema(description = "描述")
    private String description;

    @NotBlank(message = "更新人不能为空")
    @Schema(description = "更新人")
    private String updateBy;

}
