package com.tecdo.galaxy.supplier.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 供应商主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Getter
@Setter
@ToString
@TableName("supplier_info")
@Schema(name = "SupplierInfo", description = "供应商主表")
public class SupplierInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 供应商编号(规则:GYS-yy-mm-dd-0001)
     */
    @TableField("supplier_code")
    @Schema(description = "供应商编号(规则:GYS-yy-mm-dd-0001)")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @TableField("name")
    @Schema(description = "供应商名称")
    private String name;

    /**
     * 统一社会信用代码
     */
    @TableField("credit_code")
    @Schema(description = "统一社会信用代码")
    private String creditCode;

    /**
     * 注册地址
     */
    @Schema(description = "注册地址")
    @TableField("register_address")
    private String registerAddress;

    /**
     * 法定代表人
     */
    @TableField("legal_person")
    @Schema(description = "法定代表人")
    private String legalPerson;

    /**
     * 主营业务
     */
    @TableField("main_business")
    @Schema(description = "主营业务")
    private String mainBusiness;

    /**
     * 注册资本(万元)
     */
    @Schema(description = "注册资本(万元)")
    @TableField("registered_capital")
    private BigDecimal registeredCapital;

    /**
     * 营业开始日期
     */
    @Schema(description = "营业开始日期")
    @TableField("business_start_date")
    private LocalDate businessStartDate;

    /**
     * 营业期限
     */
    @TableField("business_term")
    @Schema(description = "营业期限")
    private String businessTerm;

    /**
     * 供应商类型id
     */
    @TableField("type_id")
    @Schema(description = "供应商类型id")
    private Long typeId;

    /**
     * 供应商联系人姓名
     */
    @TableField("contact_name")
    @Schema(description = "供应商联系人姓名")
    private String contactName;

    /**
     * 供应商联系人电话
     */
    @TableField("contact_phone")
    @Schema(description = "供应商联系人电话")
    private String contactPhone;

    /**
     * 供应商联系人邮箱
     */
    @TableField("contact_email")
    @Schema(description = "供应商联系人邮箱")
    private String contactEmail;

    /**
     * 开户行
     */
    @TableField("bank_name")
    @Schema(description = "开户行")
    private String bankName;

    /**
     * 银行账号
     */
    @Schema(description = "银行账号")
    @TableField("bank_account_no")
    private String bankAccountNo;

    /**
     * 账户户名
     */
    @Schema(description = "账户户名")
    @TableField("bank_account_name")
    private String bankAccountName;

    /**
     * 合作次数
     */
    @Schema(description = "合作次数")
    @TableField("cooperation_count")
    private Integer cooperationCount;

    /**
     * 交付速度分
     */
    @Schema(description = "交付速度分")
    @TableField("delivery_speed_score")
    private BigDecimal deliverySpeedScore;

    /**
     * 质量合格率
     */
    @Schema(description = "质量合格率")
    @TableField("quality_pass_score")
    private BigDecimal qualityPassScore;

    /**
     * 服务响应速度得分
     */
    @Schema(description = "服务响应速度得分")
    @TableField("service_response_score")
    private BigDecimal serviceResponseScore;

    /**
     * 价格竞争力得分
     */
    @TableField("price_score")
    @Schema(description = "价格竞争力得分")
    private BigDecimal priceScore;

    /**
     * 风险预警次数
     */
    @TableField("risk_count")
    @Schema(description = "风险预警次数")
    private Integer riskCount;

    /**
     * 最近评分
     */
    @TableField("score")
    @Schema(description = "最近评分")
    private BigDecimal score;

    /**
     * 当前评级(A/B/C/D)
     */
    @TableField("rating")
    @Schema(description = "当前评级(A/B/C/D)")
    private String rating;

    /**
     * 评级更新时间
     */
    @Schema(description = "评级更新时间")
    @TableField("rating_update_time")
    private LocalDateTime ratingUpdateTime;

    /**
     * 状态(VALID:正常,INVALID:失效)
     */
    @TableField("status")
    @Schema(description = "状态(VALID:正常,INVALID:失效)")
    private String status;

    /**
     * 创建人id
     */
    @TableField("create_by")
    @Schema(description = "创建人id")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人id
     */
    @TableField("update_by")
    @Schema(description = "更新人id")
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @TableField("current_rating")
    private String currentRating;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    @Schema(description = "租户id")
    private String tenantId;
}
