package com.tecdo.galaxy.supplier.dao.generator;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.google.common.base.Joiner;

import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * Date：2025/7/4 10:20
 * Description：mybatis代码生成器
 *
 * <AUTHOR>
 * @version v1.0.0
 */
public class MybatisPlusCodeGenerator {
    /**
     * 作者，需要修改
     */
    private static  final String AUTHOR = "huan";
    /**
     * 数据库连接信息，需要修改
     */
    private static final String JDBC_URL = "***************************************************************************";

    /**
     * 数据库用户名，需要修改
     */
    private static final String USER_NAME = "galaxy-dev01-rw";

    /**
     * 数据库密码，需要修改
     */
    private static final String PWD = "Ke9wQ585j";

    /**
     * 需要生成的表名，多个表用,分割
     */
    private static final List<String> TABLE_NAME_LIST = List.of("supplier_type");


    public static void main(String[] args) {
        String projectRoot = System.getProperty("user.dir");
        String moduleRoot  = projectRoot + File.separator + new File(projectRoot).getName() + "-dao";
        String parentPackage = getParentPackage(MybatisPlusCodeGenerator.class, 1);
        FastAutoGenerator.create(JDBC_URL, USER_NAME, PWD)
                .globalConfig(builder -> {
                    builder.author(AUTHOR) // 设置作者
                            .commentDate("yyyy-MM-dd")  // 注释日期
                            .disableOpenDir() //禁止生成后打开目录
                            .enableSpringdoc()
                            .outputDir( moduleRoot + "/src/main/java"); // 输出到dao模块的src/main/java目录
                })
                .packageConfig(builder -> {
                    builder.parent(parentPackage) // 设置父包名
                            //.moduleName("") // 设置父包模块名,目前不需要
                            .entity("entity") // 设置实体类包名
                            .mapper("mapper") // 设置 Mapper 接口包名
                            .pathInfo(Collections.singletonMap(OutputFile.xml, moduleRoot + "/src/main/resources/mapper")); // 设置 Mapper XML 文件包名
                })
                .strategyConfig(builder -> {
                    builder.addInclude(Joiner.on(",").join(TABLE_NAME_LIST)) // 设置需要生成的表名
                            .entityBuilder()
                            .enableLombok() // 启用 Lombok
                            .enableTableFieldAnnotation() // 启用字段注解
                            .enableFileOverride()
                            .mapperBuilder()
                            .enableMapperAnnotation() // 启用 @Mapper 注解
                            .serviceBuilder().disable()
                            .controllerBuilder().disable();
                })
                .templateEngine(new FreemarkerTemplateEngine()) // 使用 Freemarker 模板引擎
                .execute();
    }


    /**
     * 获取指定类的父包名
     *
     * @param clazz 目标类
     * @param levels 向上追溯的层级数
     * @return 父包名（如果存在）
     */
    public static String getParentPackage(Class<?> clazz, int levels) {
        // 验证输入参数有效性
        if (clazz == null || levels <= 0) {
            return null;
        }

        // 获取类的完整包名
        String packageName = Optional.ofNullable(clazz.getPackage())
                .map(Package::getName)
                .orElse("");

        // 分割包名为各个部分
        String[] parts = packageName.split("\\.");

        // 检查包层级是否足够
        if (parts.length <= levels) {
            return null;
        }

        // 构建父包名
        StringBuilder parentPackage = new StringBuilder(parts[0]);
        for (int i = 1; i < parts.length - levels; i++) {
            parentPackage.append(".").append(parts[i]);
        }

        return parentPackage.toString();
    }
}
