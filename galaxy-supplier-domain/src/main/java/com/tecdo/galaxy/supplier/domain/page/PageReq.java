package com.tecdo.galaxy.supplier.domain.page;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Date：2025/7/19 12:00
 * Description：分页请求入参
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class PageReq {

    @Schema(description = "页码", example = "1", defaultValue = "1")
    private int pageNum = 1;

    @Schema(description = "每页大小", example = "10", defaultValue = "10")
    private int pageSize = 10;

    // 可选：排序字段和排序方式
    @Schema(description = "排序字段", example = "id")
    private String sortBy;

    @Schema(description = "是否降序", example = "true", defaultValue = "true")
    // 可选：是否降序
    private boolean isDesc = true;
}
