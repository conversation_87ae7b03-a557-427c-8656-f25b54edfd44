# galaxy-supplier

项目说明

供应商服务服务端

## 简单分层

默认生成的项目代码采用简单分层，共五个模块，模块之间单向依赖：

```mermaid
graph TD
start --> galaxy-supplier-service
start --> galaxy-supplier-web
galaxy-supplier-service --> galaxy-supplier-dao
galaxy-supplier-web --> galaxy-supplier-api
galaxy-supplier-web --> galaxy-supplier-service
galaxy-supplier-client --> galaxy-supplier-api
```

* galaxy-supplier-dao：属于基础设施（Infrastructure）层，这里不用Infrastructure命名，主要是因为大部分业务场景是数据库的操作，为了方便理解直接叫DAO层。DAO层的实现比较独立，定义了DO模型和Mapper接口，不依赖其他模块。
* galaxy-supplier-api：是接口定义层，定义了DTO模型和API接口，不依赖其他模块。独立成模块是为了独立发布，方便客户端和服务服务端共享模型。
* galaxy-supplier-service：是业务逻辑层，所有业务相关的逻辑都在这个模块中实现。依赖DAO层，定义Model模型，需要将DAO层的DO模型转成Service层的Model模型。【简化】api模块不定义dto使用model可以，反过来不行。且api不能依赖service层。
* galaxy-supplier-web：是web控制层，提供RESTfull API给前端或者其他后端服务调用。依赖API，RESTfull API会将DTO模型转成JSON格式数据返回给客户端。
* galaxy-supplier-client：是RESTfull API的客户端，只依赖API，实现接口的调用，方便第三方集成使用。需要确保依赖包简单，不要将service/dao层的依赖包也带到这里来。
* start：是项目启动入口，通常只放置main函数。依赖Web和Service，通常还需要引入需要自动装配的依赖包。

## MJGA规范文档
[点击查看](https://git.tec-do.com/mjga/mjga/blob/master/README.md)

