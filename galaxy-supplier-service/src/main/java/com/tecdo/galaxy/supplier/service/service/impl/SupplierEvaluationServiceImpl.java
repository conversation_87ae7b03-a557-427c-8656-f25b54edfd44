package com.tecdo.galaxy.supplier.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tecdo.galaxy.supplier.dao.entity.SupplierEvaluationRecord;
import com.tecdo.galaxy.supplier.dao.entity.SupplierFileInfo;
import com.tecdo.galaxy.supplier.dao.entity.SupplierInfo;
import com.tecdo.galaxy.supplier.domain.dto.tablequery.SupplierEvaluationRecordQuery;
import com.tecdo.galaxy.supplier.domain.dto.tablequery.SupplierInfoQuery;
import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.FileInfoDTO;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.ListEvaluationQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.AddEvaluationDTO;
import com.tecdo.galaxy.supplier.domain.req.evaluation.ListCurrUserEvaluationRecordQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.ListHisEvaluationQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.UpdateEvaluationDTO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.ListTotalScoreEvaluationVO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.EvaluationDetailVO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.ListCurrUserEvaluationRecordVO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.ListHisEvaluationVO;
import com.tecdo.galaxy.supplier.service.enums.ExecuteStatusEnum;
import com.tecdo.galaxy.supplier.service.enums.SupplierFileTypeEnum;
import com.tecdo.galaxy.supplier.service.repository.SupplierEvaluationRecordRepository;
import com.tecdo.galaxy.supplier.service.repository.SupplierFileInfoRepository;
import com.tecdo.galaxy.supplier.service.repository.SupplierInfoRepository;
import com.tecdo.galaxy.supplier.service.service.SupplierEvaluationService;
import com.tecdo.galaxy.supplier.service.util.PageUtil;
import com.tecdo.mac.mjga.common.exception.BizException;
import com.tecdo.mac.mjga.common.exception.SysException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Date：2025/7/18 17:48 Description：供应商评价服务
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Slf4j
@Service
public class SupplierEvaluationServiceImpl implements SupplierEvaluationService {

    @Resource
    private SupplierEvaluationRecordRepository supplierEvaluationRecordRepository;

    @Resource
    private SupplierFileInfoRepository supplierFileInfoRepository;

    @Resource
    private SupplierInfoRepository supplierInfoRepository;

    @Override
    public boolean addEvaluation(AddEvaluationDTO addEvaluationDTO) {
        SupplierEvaluationRecord entity = BeanUtil.copyProperties(addEvaluationDTO, SupplierEvaluationRecord.class);
        entity.setEvaluationStatus(ExecuteStatusEnum.INIT.getCode());
        return supplierEvaluationRecordRepository.insert(entity) > 0;
    }

    @Override
    public boolean updateEvaluation(UpdateEvaluationDTO updateEvaluationDTO) {
        long evaluationRecordId = updateEvaluationDTO.getId();
        checkExistEvaluationRecord(evaluationRecordId);
        SupplierEvaluationRecord preUpdate =
            BeanUtil.copyProperties(updateEvaluationDTO, SupplierEvaluationRecord.class);
        preUpdate.setEvaluationStatus(ExecuteStatusEnum.FINISH.getCode());
        preUpdate.setEvaluationTime(LocalDateTime.now());
        int updateCount = supplierEvaluationRecordRepository.updateById(preUpdate);
        if (updateCount <= 0) {
            log.error("Failed to update supplier info, supplierCode: {}", preUpdate.getSupplierCode());
            throw new SysException("Failed to update supplier info");
        }

        List<SupplierFileInfo> supplierFileInfoList =
            transferFileInfoList2SupplierFileInfoList(updateEvaluationDTO.getFileInfoList(), evaluationRecordId);
        if (CollectionUtil.isNotEmpty(supplierFileInfoList)) {
            int insertedFiles = supplierFileInfoRepository.batchInsert(supplierFileInfoList);
            if (insertedFiles != supplierFileInfoList.size()) {
                log.error("Failed to insert file info, supplierCode: {}", preUpdate.getSupplierCode());
                throw new SysException("Failed to insert file info");
            }
        }
        return true;
    }

    private void checkExistEvaluationRecord(long evaluationRecordId) {
        SupplierEvaluationRecord oldEntity = supplierEvaluationRecordRepository.getById(evaluationRecordId);
        if (Objects.isNull(oldEntity)) {
            log.error("supplierEvaluationRecord not found, evaluationRecordId: {}", evaluationRecordId);
            throw new BizException("supplierEvaluationRecord not found");
        }

        if (ExecuteStatusEnum.FINISH.getCode().equals(oldEntity.getEvaluationStatus())) {
            throw new SysException("invalid supplierEvaluationRecord status");
        }
    }

    private List<SupplierFileInfo> transferFileInfoList2SupplierFileInfoList(List<FileInfoDTO> fileInfoList,
        long evaluationRecordId) {
        return fileInfoList.stream().map(file -> {
            SupplierFileInfo supplierFileInfo = new SupplierFileInfo();
            supplierFileInfo.setBizId(String.valueOf(evaluationRecordId));
            supplierFileInfo.setBizType(SupplierFileTypeEnum.SUPPLIER_EVALUATION_FILE.getCode());
            supplierFileInfo.setFileUrl(file.getFileUrl());
            supplierFileInfo.setSeq(file.getSeq());
            return supplierFileInfo;
        }).collect(Collectors.toList());
    }

    @Override
    public EvaluationDetailVO getEvaluationDetail(Long id) {
        SupplierEvaluationRecord oldEntity = supplierEvaluationRecordRepository.getById(id);
        if (Objects.isNull(oldEntity)) {
            log.error("supplierEvaluationRecord not found, id: {}", id);
            throw new BizException("supplierEvaluationRecord not found");
        }
        EvaluationDetailVO.EvaluationBaseInfo baseInfo = new EvaluationDetailVO.EvaluationBaseInfo();
        BeanUtil.copyProperties(oldEntity, baseInfo);
        EvaluationDetailVO resp = new EvaluationDetailVO();
        resp.setBaseInfo(baseInfo);
        return resp;
    }

    @Override
    public PageResp<ListTotalScoreEvaluationVO> listTotalScoreEvaluation(ListEvaluationQuery listEvaluationQuery) {
        SupplierInfoQuery query = BeanUtil.copyProperties(listEvaluationQuery, SupplierInfoQuery.class);
        IPage<SupplierInfo> pageResult = supplierInfoRepository.listPage(
            Page.of(listEvaluationQuery.getPageNum(), listEvaluationQuery.getPageSize()), query);
        return PageUtil.convert(pageResult, o -> BeanUtil.copyProperties(o, ListTotalScoreEvaluationVO.class));
    }

    @Override
    public PageResp<ListCurrUserEvaluationRecordVO> listSupplierEvaluationRecord(
        ListCurrUserEvaluationRecordQuery listCurrUserEvaluationRecordQuery) {
        SupplierEvaluationRecordQuery query =
            BeanUtil.copyProperties(listCurrUserEvaluationRecordQuery, SupplierEvaluationRecordQuery.class);
        query.setEvaluationStatus(ExecuteStatusEnum.INIT.getCode());
        IPage<SupplierEvaluationRecord> pageResult = supplierEvaluationRecordRepository.list(
            Page.of(listCurrUserEvaluationRecordQuery.getPageNum(), listCurrUserEvaluationRecordQuery.getPageSize()),
            query);
        return PageUtil.convert(pageResult, o -> BeanUtil.copyProperties(o, ListCurrUserEvaluationRecordVO.class));
    }

    @Override
    public PageResp<ListHisEvaluationVO> listHisEvaluation(ListHisEvaluationQuery listHisEvaluationQuery) {
        SupplierEvaluationRecordQuery query =
            BeanUtil.copyProperties(listHisEvaluationQuery, SupplierEvaluationRecordQuery.class);
        query.setEvaluationStatus(ExecuteStatusEnum.FINISH.getCode());
        IPage<SupplierEvaluationRecord> pageResult = supplierEvaluationRecordRepository.list(
            Page.of(listHisEvaluationQuery.getPageNum(), listHisEvaluationQuery.getPageSize()), query);
        return PageUtil.convert(pageResult, o -> BeanUtil.copyProperties(o, ListHisEvaluationVO.class));
    }
}
