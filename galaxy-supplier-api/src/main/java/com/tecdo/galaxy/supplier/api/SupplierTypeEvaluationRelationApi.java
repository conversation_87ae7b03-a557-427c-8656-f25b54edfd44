package com.tecdo.galaxy.supplier.api;

import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation.AddRelationDTO;
import com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation.ListTypeEvaluationRelationQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation.UpdateRelationDTO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.tyepefactorrelation.ListTypeEvaluationRelationVO;
import com.tecdo.mac.mjga.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Date：2025/7/21 19:41 Description：供应商类型评价关系api
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Tag(name = "供应商类型评价要素关联关系相关接口")
public interface SupplierTypeEvaluationRelationApi {

    /**
     * 供应商类型评价要素关联关系列表
     *
     * @param listTypeEvaluationRelationQuery 供应商类型评价要素关联关系查询参数
     * @return 供应商类型评价要素关联关系列表
     */
    @GetMapping("/list-page")
    @Operation(summary = "查询关联关系列表")
    Result<PageResp<ListTypeEvaluationRelationVO>> listPage(
        @ParameterObject ListTypeEvaluationRelationQuery listTypeEvaluationRelationQuery);

    /**
     * 添加关联关系
     *
     * @param addRelationDTO
     * @return
     */
    @PostMapping("/add")
    @Operation(summary = "添加关联关系")
    Result<Boolean> addRelation(@RequestBody @Valid AddRelationDTO addRelationDTO);

    /**
     * 更新关联关系
     *
     * @param updateRelationDTO
     * @return
     */
    @PostMapping("/update")
    @Operation(summary = "更新关联关系")
    Result<Boolean> updateRelation(@RequestBody @Valid UpdateRelationDTO updateRelationDTO);

}
