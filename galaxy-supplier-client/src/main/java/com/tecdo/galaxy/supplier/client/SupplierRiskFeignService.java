package com.tecdo.galaxy.supplier.client;

import com.tecdo.galaxy.supplier.api.SupplierRiskApi;
import com.tecdo.mac.mjga.web.feign.annotation.FeignService;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @version V1.0.0
 * @Description: 供应商风险feign服务
 * @author: huan
 * @date: 2025/7/23 18:09
 * @Copyright:
 */
@FeignService
@RequestMapping("/supplier/risk")
public interface SupplierRiskFeignService extends SupplierRiskApi {

}
