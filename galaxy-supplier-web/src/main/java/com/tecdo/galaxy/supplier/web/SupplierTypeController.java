package com.tecdo.galaxy.supplier.web;

import com.tecdo.galaxy.supplier.api.SupplierTypeApi;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.AddTypeDTO;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.BatchManagerSupplierTypeDTO;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.ListSupplierTypeQuery;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.UpdateTypeDTO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.type.SupplierTypeTreeVO;
import com.tecdo.galaxy.supplier.service.service.SupplierTypeService;
import com.tecdo.mac.mjga.common.response.Result;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @version V1.0.0
 * @Description: 供应商类型接口实现
 * @author: huan
 * @date: 2025/7/21 17:47
 * @Copyright:
 */
@Validated
@RestController
public class SupplierTypeController implements SupplierTypeApi {

    @Resource
    private SupplierTypeService supplierTypeService;

    @Override
    public Result<Boolean> addType(AddTypeDTO addTypeDTO) {
        return Result.success(supplierTypeService.addType(addTypeDTO));
    }

    @Override
    public Result<Boolean> updateType(UpdateTypeDTO updateTypeDTO) {
        return Result.success(supplierTypeService.updateType(updateTypeDTO));
    }

    @Override
    public Result<List<SupplierTypeTreeVO>> listTree(ListSupplierTypeQuery listSupplierTypeQuery) {
        return Result.success(supplierTypeService.listTree(listSupplierTypeQuery));
    }

    @Override
    public Result<Boolean> batchManagerSupplierType(BatchManagerSupplierTypeDTO batchManagerSupplierTypeDTO) {
        return Result.success(supplierTypeService.batchManagerSupplierType(batchManagerSupplierTypeDTO));
    }
}