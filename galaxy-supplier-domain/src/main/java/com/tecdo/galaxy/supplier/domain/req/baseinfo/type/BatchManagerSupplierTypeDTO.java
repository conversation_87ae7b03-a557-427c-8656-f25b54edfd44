package com.tecdo.galaxy.supplier.domain.req.baseinfo.type;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * Date：2025/7/27 19:52 Description：批量管理供应商类型
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class BatchManagerSupplierTypeDTO {

    /**
     * 供应商类型id
     */
    @NotNull(message = "供应商类型id不能为空")
    private Long typeId;

    /**
     * 供应商code列表
     */
    @NotNull(message = "供应商编码列表不能为空")
    @Size(min = 1, message = "供应商编码列表不能为空")
    private List<@NotBlank(message = "供应商编码不能为空") String> supplierCodeList;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;

}
