package com.tecdo.galaxy.supplier.domain.req.evaluation;

import com.tecdo.galaxy.supplier.domain.page.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Date：2025/7/19 20:26
 * Description：历史供应商评价列表请求体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class ListHisEvaluationQuery extends PageReq {

    @Schema(description = "供应商编号")
    private String supplierCode;

    @Schema(description = "评分时间左区间")
    private LocalDateTime evaluationTimeStart;

    @Schema(description = "评分时间右区间")
    private LocalDateTime evaluationTimeEnd;

    @Schema(description = "采购单号")
    private String purchaseId;

}
