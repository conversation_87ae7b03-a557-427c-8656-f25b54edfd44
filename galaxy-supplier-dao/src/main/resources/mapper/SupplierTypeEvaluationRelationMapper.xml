<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tecdo.galaxy.supplier.dao.mapper.SupplierTypeEvaluationRelationMapper">

    <select id="listTypeEvaluationRelationJoin" resultType="com.tecdo.galaxy.supplier.dao.entity.manual.SupplierTypeEvaluationRelationJoinDTO">
        SELECT
            st.name AS typeName,
            sef.factor_name AS factorName,
            ster.weight AS weight,
            ster.update_time AS updateTime
        FROM
            supplier_type st
                LEFT JOIN
            supplier_type_evaluation_relation ster ON st.id = ster.type_id
                LEFT JOIN
            supplier_evaluation_factor sef ON ster.factor_id = sef.id
        <if test="typeName != null and typeName != ''">
            WHERE st.name LIKE CONCAT(#{typeName}, '%')
        </if>
        ORDER BY
            st.id
    </select>

    <insert id="batchInsert">
        INSERT INTO
        supplier_type_evaluation_relation(type_id, factor_id, weight, create_by)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.typeId}, #{item.factorId}, #{item.weight}, #{item.createBy})
        </foreach>
    </insert>
</mapper>