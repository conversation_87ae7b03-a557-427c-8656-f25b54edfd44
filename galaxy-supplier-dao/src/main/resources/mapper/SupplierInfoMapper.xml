<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tecdo.galaxy.supplier.dao.mapper.SupplierInfoMapper">

    <insert id="batchUpdateTypeIdBySupplierCode">
        UPDATE supplier_info
        SET type_id = #{typeId}, update_by = #{updateBy}
        WHERE supplier_code IN
        <foreach collection="supplierCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </insert>
</mapper>
