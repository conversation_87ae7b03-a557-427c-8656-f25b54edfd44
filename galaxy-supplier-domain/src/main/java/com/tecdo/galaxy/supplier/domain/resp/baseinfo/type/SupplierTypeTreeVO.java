package com.tecdo.galaxy.supplier.domain.resp.baseinfo.type;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * Date：2025/7/31 19:30
 * Description：供应商类型树形结构响应体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
@Schema(name = "SupplierTypeTreeVO", description = "供应商类型树形结构响应体")
public class SupplierTypeTreeVO {

    @Schema(description = "供应商类型ID")
    private Long id;

    @Schema(description = "父分类ID，顶级分类为0")
    private Long parentId;

    @Schema(description = "供应商类型名称")
    private String name;

    @Schema(description = "供应商类型描述")
    private String description;

    @Schema(description = "分类路径，格式如:1/2/3")
    private String path;

    @Schema(description = "分类层级")
    private Integer level;

    @Schema(description = "子分类列表")
    private List<SupplierTypeTreeVO> children;
}