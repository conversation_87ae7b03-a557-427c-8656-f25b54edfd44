package com.tecdo.galaxy.supplier.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tecdo.galaxy.supplier.dao.entity.SupplierInfo;
import com.tecdo.galaxy.supplier.domain.dto.tablequery.SupplierInfoQuery;
import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.rating.ListRatingQuery;
import com.tecdo.galaxy.supplier.domain.resp.rating.ListRatingVO;
import com.tecdo.galaxy.supplier.service.repository.SupplierInfoRepository;
import com.tecdo.galaxy.supplier.service.service.SupplierRatingService;
import com.tecdo.galaxy.supplier.service.util.PageUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Date：2025/7/21 14:54
 * Description：供应商评级服务实现类
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Service
public class SupplierRatingServiceImpl implements SupplierRatingService {

    @Resource
    private SupplierInfoRepository supplierInfoRepository;

    @Override
    public PageResp<ListRatingVO> listRatingPage(ListRatingQuery listRatingQuery) {
        SupplierInfoQuery query = BeanUtil.copyProperties(listRatingQuery, SupplierInfoQuery.class);
        IPage<SupplierInfo> pageResult =
            supplierInfoRepository.listPage(Page.of(listRatingQuery.getPageNum(), listRatingQuery.getPageSize()),
                query);
        return PageUtil.convert(pageResult, o -> BeanUtil.copyProperties(o, ListRatingVO.class));
    }
}
