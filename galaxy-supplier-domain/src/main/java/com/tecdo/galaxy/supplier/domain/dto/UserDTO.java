package com.tecdo.galaxy.supplier.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "用户信息DTO")
public class UserDTO implements Serializable {

    @Schema(description = "用户ID", example = "1")
    private Integer id;

    @Schema(description = "用户名称", example = "张三")
    private String name;

    @Schema(description = "手机号码", example = "13800138000")
    private String phone;

}
