package com.tecdo.galaxy.supplier.service.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tecdo.galaxy.supplier.dao.entity.SupplierEvaluationRecord;
import com.tecdo.galaxy.supplier.dao.mapper.SupplierEvaluationRecordMapper;
import com.tecdo.galaxy.supplier.domain.dto.tablequery.SupplierEvaluationRecordQuery;
import com.tecdo.galaxy.supplier.service.enums.ExecuteStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * Date：2025/7/9 18:06 Description：供应商信息Repository
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Repository
public class SupplierEvaluationRecordRepository {

    @Resource
    private SupplierEvaluationRecordMapper supplierEvaluationRecordMapper;

    public IPage<SupplierEvaluationRecord> list(Page<SupplierEvaluationRecord> page,
        SupplierEvaluationRecordQuery query) {
        LambdaQueryWrapper<SupplierEvaluationRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(query.getSupplierCode()), SupplierEvaluationRecord::getSupplierCode,
                query.getSupplierCode())
            .ge(query.getPurchaseAcceptanceTimeStart() != null, SupplierEvaluationRecord::getPurchaseAcceptanceTime,
                query.getPurchaseAcceptanceTimeStart())
            .le(query.getPurchaseAcceptanceTimeEnd() != null, SupplierEvaluationRecord::getPurchaseAcceptanceTime,
                query.getPurchaseAcceptanceTimeEnd())
            .eq(StringUtils.isNotBlank(query.getEvaluationStatus()), SupplierEvaluationRecord::getEvaluationStatus,
                query.getEvaluationStatus())
            .eq(StrUtil.isNotBlank(query.getSupplierCode()), SupplierEvaluationRecord::getSupplierCode,
                query.getSupplierCode())
            .eq(StrUtil.isNotBlank(query.getPurchaseId()), SupplierEvaluationRecord::getPurchaseId,
                query.getPurchaseId())
            .eq(query.getEvaluationTimeStart() != null, SupplierEvaluationRecord::getEvaluationTime,
                query.getEvaluationTimeStart())
            .le(query.getEvaluationTimeEnd() != null, SupplierEvaluationRecord::getEvaluationTime,
                query.getEvaluationTimeEnd());
        return supplierEvaluationRecordMapper.selectPage(page, wrapper);
    }

    public int insert(SupplierEvaluationRecord entity) {
        return supplierEvaluationRecordMapper.insert(entity);
    }

    public int updateById(SupplierEvaluationRecord entity) {
        return supplierEvaluationRecordMapper.updateById(entity);
    }

    public SupplierEvaluationRecord getById(Long id) {
        return supplierEvaluationRecordMapper.selectById(id);
    }

    public List<SupplierEvaluationRecord> listBySupplierCode(String supplierCode) {
        LambdaQueryWrapper<SupplierEvaluationRecord> wrapper =
            new LambdaQueryWrapper<SupplierEvaluationRecord>().eq(SupplierEvaluationRecord::getSupplierCode,
                supplierCode).eq(SupplierEvaluationRecord::getEvaluationStatus, ExecuteStatusEnum.FINISH.getCode());
        return supplierEvaluationRecordMapper.selectList(wrapper);
    }

}
