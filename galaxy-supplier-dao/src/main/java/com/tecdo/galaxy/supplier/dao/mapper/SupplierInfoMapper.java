package com.tecdo.galaxy.supplier.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tecdo.galaxy.supplier.dao.entity.SupplierInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 供应商主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Mapper
public interface SupplierInfoMapper extends BaseMapper<SupplierInfo> {

    int batchUpdateTypeIdBySupplierCode(List<String> supplierCodeList, Long typeId, String updateBy);

}
