package com.tecdo.galaxy.supplier.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Date：2025/7/8 10:11
 * Description：分页对象
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
@Schema(description = "分页信息")
public class PageInfo {

    @Schema(description = "页码", example = "1", defaultValue = "1")
    private int pageNum = 1;

    @Schema(description = "每页大小", example = "10", defaultValue = "10")
    private int pageSize = 10;

}
