package com.tecdo.galaxy.supplier.domain.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * Date：2025/7/28 16:19 Description：校验允许为空但是不允许为空字符串
 *
 * <AUTHOR>
 * @version v1.0.0
 */
public class NullableNotBlankValidator implements ConstraintValidator<NullableNotBlank, String> {

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        // 允许null值,但不允许空字符串
        if (s == null) {
            return true;
        }
        return !s.isEmpty();
    }
}
