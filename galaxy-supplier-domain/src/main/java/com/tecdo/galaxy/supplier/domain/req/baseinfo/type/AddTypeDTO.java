package com.tecdo.galaxy.supplier.domain.req.baseinfo.type;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * Date：2025/7/27 19:08 Description：新增供应商类型请求体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class AddTypeDTO {

    @NotBlank(message = "类型名称不能为空")
    @Schema(description = "类型名称")
    private String name;

    @Schema(description = "类型描述")
    private String description;

    @Positive(message = "创建人非法")
    @Schema(description = "创建人")
    private Long createBy;
}
