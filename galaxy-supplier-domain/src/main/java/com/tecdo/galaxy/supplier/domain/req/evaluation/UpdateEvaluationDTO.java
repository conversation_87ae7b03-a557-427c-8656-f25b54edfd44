package com.tecdo.galaxy.supplier.domain.req.evaluation;

import com.tecdo.galaxy.supplier.domain.req.FileInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Date：2025/7/16 21:02
 * Description：更新供应商评价请求体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class UpdateEvaluationDTO {

    /**
     * 供应商评分记录id
     */
    @Schema(description = "id", required = true)
    @NotNull(message = "id不能为空")
    private long id;
    /**
     * 交付速度分
     */
    @Schema(description = "交付速度分", required = true)
    @NotNull(message = "交付速度分不能为空")
    @DecimalMin(value = "0.0", message = "交付速度分不能小于0")
    @DecimalMax(value = "5.0", message = "交付速度分不能大于5")
    private BigDecimal deliverySpeedScore;

    /**
     * 质量合格率
     */
    @Schema(description = "质量合格率", required = true)
    @NotNull(message = "质量合格率不能为空")
    @DecimalMin(value = "0.0", message = "质量合格率不能小于0")
    @DecimalMax(value = "5.0", message = "质量合格率不能大于5")
    private BigDecimal qualityPassScore;

    /**
     * 服务响应速度得分
     */
    @Schema(description = "服务响应速度得分", required = true)
    @NotNull(message = "服务响应速度得分不能为空")
    @DecimalMin(value = "0.0", message = "服务响应速度得分不能小于0")
    @DecimalMax(value = "5.0", message = "服务响应速度得分不能大于5")
    private BigDecimal serviceResponseScore;

    /**
     * 价格竞争力得分
     */
    @Schema(description = "价格竞争力得分", required = true)
    @NotNull(message = "价格竞争力得分不能为空")
    @DecimalMin(value = "0.0", message = "价格竞争力得分不能小于0")
    @DecimalMax(value = "5.0", message = "价格竞争力得分不能大于5")
    private BigDecimal priceScore;

    /**
     * 总分
     */
    @Schema(description = "总分", required = true)
    @NotNull(message = "总分不能为空")
    @DecimalMin(value = "0.0", message = "总分不能小于0")
    @DecimalMax(value = "5.0", message = "总分不能大于5")
    private BigDecimal score;

    /**
     * 评分内容
     */
    @Schema(description = "评分内容", required = true)
    private String evaluationContent;

    /**
     * 评分文件
     */
    @Schema(description = "评分文件")
    @Valid
    private List<FileInfoDTO> fileInfoList;

}
