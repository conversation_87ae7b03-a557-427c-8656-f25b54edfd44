package com.tecdo.galaxy.supplier.domain.req.baseinfo.type;

import com.tecdo.galaxy.supplier.domain.validation.NullableNotBlank;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * Date：2025/7/27 19:08 Description：更新供应商类型请求体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class UpdateTypeDTO {

    @Schema(description = "类型id")
    @Min(value = 1, message = "类型id不能小于1")
    private Long id;

    @NullableNotBlank(message = "类型名称不能为空字符串")
    @Schema(description = "类型名称")
    private String name;

    @NullableNotBlank(message = "类型描述不能为空字符串")
    @Schema(description = "类型描述")
    private String description;

    @NotBlank(message = "更新人不能为空")
    @Schema(description = "更新人")
    private String updateBy;
}
