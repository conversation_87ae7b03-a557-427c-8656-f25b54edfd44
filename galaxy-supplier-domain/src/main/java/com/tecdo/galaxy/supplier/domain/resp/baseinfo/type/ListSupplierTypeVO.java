package com.tecdo.galaxy.supplier.domain.resp.baseinfo.type;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Date：2025/7/21 17:55 Description:查询供应商类型列表响应体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class ListSupplierTypeVO {

    @Schema(description = "供应商类型id", example = "1")
    private Long id;

    @Schema(description = "供应商类型名称", example = "生产型")
    private String name;

    @Schema(description = "供应商类型描述", example = "生产型供应商")
    private String description;

}
