package com.tecdo.galaxy.supplier.service.enums;

import lombok.Getter;

/**
 * @version V1.0.0
 * @Description:
 * @author: huan
 * @date: 2025/7/19 18:45
 * @Copyright:
 */
public enum ExecuteStatusEnum {

    INIT("INIT", "初始化"),

    FINISH("FINISH", "完成");

    @Getter
    private String code;

    @Getter
    private String desc;

    ExecuteStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
