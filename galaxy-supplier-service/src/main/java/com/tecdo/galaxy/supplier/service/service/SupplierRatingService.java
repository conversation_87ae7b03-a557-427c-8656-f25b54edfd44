package com.tecdo.galaxy.supplier.service.service;

import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.rating.ListRatingQuery;
import com.tecdo.galaxy.supplier.domain.resp.rating.ListRatingVO;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Date：2025/7/21 12:27
 * Description：供应商服务评级
 *
 * <AUTHOR>
 * @version v1.0.0
 */
public interface SupplierRatingService {

    /**
     * 查询评级列表
     * @param listRatingQuery
     * @return
     */
    PageResp<ListRatingVO> listRatingPage(@RequestBody ListRatingQuery listRatingQuery);

}
