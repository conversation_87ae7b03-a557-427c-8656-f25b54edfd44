package com.tecdo.galaxy.supplier.domain.resp.evaluation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Date：2025/7/19 20:26
 * Description：历史供应商评价列表想银泰
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class ListHisEvaluationVO {

    @Schema(description = "采购单号")
    private String purchaseId;

    @Schema(description = "评分时间")
    private LocalDateTime evaluationTime;

    /**
     * 交付速度分
     */
    @Schema(description = "交付速度分")
    private BigDecimal deliverySpeedScore;

    /**
     * 质量合格率
     */
    @Schema(description = "质量合格率")
    private BigDecimal qualityPassScore;

    /**
     * 服务响应速度得分
     */
    @Schema(description = "服务响应速度得分")
    private BigDecimal serviceResponseScore;

    /**
     * 价格竞争力得分
     */
    @Schema(description = "价格竞争力得分")
    private BigDecimal priceScore;

    /**
     * 总分
     */
    @Schema(description = "总分")
    private BigDecimal score;

}
