package com.tecdo.galaxy.supplier.web;

import com.tecdo.galaxy.supplier.api.UserApi;
import com.tecdo.galaxy.supplier.service.service.UserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@Validated
@RequestMapping("/user")
public class UserController implements UserApi {
    @Resource
    private UserService userService;

    @Value("${apollo.conf:1}")
    private String apolloConf;

    @Override
    public long selectUserCount() {
        return userService.selectUserCount();
    }

    @GetMapping("/apollo")
    public String getApollo(){
        return apolloConf;
    }
}
