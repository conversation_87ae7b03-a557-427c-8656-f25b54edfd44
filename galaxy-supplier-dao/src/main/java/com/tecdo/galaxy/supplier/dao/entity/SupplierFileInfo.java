package com.tecdo.galaxy.supplier.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 供应商模块相关文件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Getter
@Setter
@ToString
@TableName("supplier_file_info")
@Schema(name = "SupplierFileInfo", description = "供应商模块相关文件表")
public class SupplierFileInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务id
     */
    @TableField("biz_id")
    @Schema(description = "业务id")
    private String bizId;

    /**
     * 业务类型:供应商基础数据文件:SUPPLIER_BASE_INFO_FILE
     */
    @TableField("biz_type")
    @Schema(description = "业务类型:供应商基础数据文件:SUPPLIER_BASE_INFO_FILE")
    private String bizType;

    /**
     * 文件类型：FILE_URL:普通文件url,FEISHU_URL:飞书文档url
     */
    @TableField("file_type")
    @Schema(description = "文件类型：FILE_URL:普通文件url,FEISHU_URL:飞书文档url")
    private String fileType;

    /**
     * 存储路径
     */
    @TableField("file_url")
    @Schema(description = "存储路径")
    private String fileUrl;

    /**
     * 排序
     */
    @TableField("seq")
    @Schema(description = "排序")
    private Integer seq;

    /**
     * 创建人
     */
    @TableField("create_by")
    @Schema(description = "创建人")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    @Schema(description = "更新人")
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    @Schema(description = "租户id")
    private String tenantId;
}
