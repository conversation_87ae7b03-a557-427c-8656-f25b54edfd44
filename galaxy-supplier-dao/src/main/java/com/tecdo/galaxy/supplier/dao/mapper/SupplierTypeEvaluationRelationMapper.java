package com.tecdo.galaxy.supplier.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tecdo.galaxy.supplier.dao.entity.SupplierTypeEvaluationRelation;
import com.tecdo.galaxy.supplier.dao.entity.manual.SupplierTypeEvaluationRelationJoinDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 供应商类型-评价要素关联关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Mapper
public interface SupplierTypeEvaluationRelationMapper extends BaseMapper<SupplierTypeEvaluationRelation> {

    IPage<SupplierTypeEvaluationRelationJoinDTO> listTypeEvaluationRelationJoin(Page page, @Param("typeName") String typeName);

    int batchInsert(List<SupplierTypeEvaluationRelation> list);

}