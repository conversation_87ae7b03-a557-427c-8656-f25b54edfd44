package com.tecdo.galaxy.supplier.service.repository;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tecdo.galaxy.supplier.dao.entity.SupplierInfo;
import com.tecdo.galaxy.supplier.dao.mapper.SupplierInfoMapper;
import com.tecdo.galaxy.supplier.domain.dto.tablequery.SupplierInfoQuery;
import com.tecdo.galaxy.supplier.service.constant.SystemConstant;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import static com.tecdo.galaxy.supplier.service.constant.SystemConstant.DASH;
import static com.tecdo.galaxy.supplier.service.constant.SystemConstant.SUPPLIER_CODE_PREFIX;

/**
 * Date：2025/7/9 18:06 Description:供应商信息Repository
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Repository
public class SupplierInfoRepository {

    @Resource
    private SupplierInfoMapper supplierInfoMapper;

    public SupplierInfo getByCreditCode(String creditCode) {
        LambdaQueryWrapper<SupplierInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierInfo::getCreditCode, creditCode)
            .eq(SupplierInfo::getStatus, SystemConstant.STATUS_VALID);
        return supplierInfoMapper.selectOne(queryWrapper);
    }

    public SupplierInfo getBySupplierCode(String supplierCode) {
        LambdaQueryWrapper<SupplierInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierInfo::getSupplierCode, supplierCode)
            .eq(SupplierInfo::getStatus, SystemConstant.STATUS_VALID);
        return supplierInfoMapper.selectOne(queryWrapper);
    }

    public int insert(SupplierInfo supplierInfo) {
        return supplierInfoMapper.insert(supplierInfo);
    }

    public int updateById(SupplierInfo supplierInfo) {
        return supplierInfoMapper.updateById(supplierInfo);
    }

    public SupplierInfo getById(Long id) {
        return supplierInfoMapper.selectById(id);
    }

    public SupplierInfo getMaxCodeSupplierInfoByDateCodePrefix(String datePart) {
        LambdaQueryWrapper<SupplierInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(SupplierInfo::getSupplierCode, SUPPLIER_CODE_PREFIX + datePart + DASH);
        queryWrapper.orderByDesc(SupplierInfo::getId);
        queryWrapper.last(SystemConstant.SQL_LIMIT_ONE);
        return supplierInfoMapper.selectOne(queryWrapper);
    }

    public IPage<SupplierInfo> listPage(Page<SupplierInfo> page, SupplierInfoQuery query) {
        return supplierInfoMapper.selectPage(page, buildCommonQueryWrapper(query));
    }

    public List<SupplierInfo> list(SupplierInfoQuery query) {
        return supplierInfoMapper.selectList(buildCommonQueryWrapper(query));
    }

    private LambdaQueryWrapper<SupplierInfo> buildCommonQueryWrapper(SupplierInfoQuery query) {
        LambdaQueryWrapper<SupplierInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotBlank(query.getSupplierCode()), SupplierInfo::getSupplierCode, query.getSupplierCode())
            .gt(ObjUtil.isNotNull(query.getMinScore()), SupplierInfo::getScore, query.getMinScore())
            .le(ObjUtil.isNotNull(query.getMaxScore()), SupplierInfo::getScore, query.getMaxScore())
            .eq(StrUtil.isNotBlank(query.getRating()), SupplierInfo::getRating, query.getRating())
            .eq(ObjUtil.isNotNull(query.getTypeId()), SupplierInfo::getTypeId, query.getTypeId())
            .le(ObjUtil.isNotNull(query.getCreateTimeEnd()), SupplierInfo::getCreateTime, query.getCreateTimeEnd())
            .gt(ObjUtil.isNotNull(query.getCreateTimeStart()), SupplierInfo::getCreateTime, query.getCreateTimeStart())
            .le(ObjUtil.isNotNull(query.getRatingUpdateTimeEnd()), SupplierInfo::getRatingUpdateTime,
                query.getRatingUpdateTimeEnd())
            .gt(ObjUtil.isNotNull(query.getRatingUpdateTimeStart()), SupplierInfo::getRatingUpdateTime,
                query.getRatingUpdateTimeStart()).orderByDesc(SupplierInfo::getCreateTime)
            .ge(StrUtil.isNotBlank(query.getMinCooperationCount()), SupplierInfo::getCooperationCount,
                query.getMinCooperationCount())
            .le(StrUtil.isNotBlank(query.getMaxCooperationCount()), SupplierInfo::getCooperationCount,
                query.getMaxCooperationCount());
        return wrapper;
    }

    public SupplierInfo getById(long id) {
        return supplierInfoMapper.selectById(id);
    }

    public List<SupplierInfo> wildcardQueryByName(String name) {
        LambdaQueryWrapper<SupplierInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(SupplierInfo::getName, name).eq(SupplierInfo::getStatus, SystemConstant.STATUS_VALID)
            .select(SupplierInfo::getSupplierCode, SupplierInfo::getName);
        return supplierInfoMapper.selectList(queryWrapper);
    }

    public int batchUpdateTypeIdBySupplierCodeList(List<String> supplierCodeList, Long typeId, String updateBy) {
        return supplierInfoMapper.batchUpdateTypeIdBySupplierCode(supplierCodeList, typeId, updateBy);
    }

}
