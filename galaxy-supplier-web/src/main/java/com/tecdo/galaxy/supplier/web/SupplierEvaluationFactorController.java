package com.tecdo.galaxy.supplier.web;

import com.tecdo.galaxy.supplier.api.SupplierEvaluationFactorApi;
import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.evaluation.factor.AddEvaluationFactorDTO;
import com.tecdo.galaxy.supplier.domain.req.evaluation.factor.ListEvaluationFactorQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.factor.UpdateEvaluationFactorDTO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.factor.ListEvaluationFactorVO;
import com.tecdo.galaxy.supplier.service.service.SupplierEvaluationFactorService;
import com.tecdo.mac.mjga.common.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Date：2025/7/21 17:31
 * Description：供应商评价要素Controller
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@RestController
@Slf4j
@Validated
@RequestMapping("/supplier/evaluation/factor")
public class SupplierEvaluationFactorController implements SupplierEvaluationFactorApi {

    @Resource
    private SupplierEvaluationFactorService supplierEvaluationFactorService;

    @Override
    public Result<Boolean> add(AddEvaluationFactorDTO addEvaluationFactorDTO) {
        return Result.success(supplierEvaluationFactorService.addType(addEvaluationFactorDTO));
    }

    @Override
    public Result<Boolean> update(UpdateEvaluationFactorDTO updateEvaluationFactorDTO) {
        return Result.success(supplierEvaluationFactorService.updateType(updateEvaluationFactorDTO));
    }

    @Override
    public Result<PageResp<ListEvaluationFactorVO>> listPage(ListEvaluationFactorQuery listEvaluationFactorQuery) {
        return Result.success(supplierEvaluationFactorService.listPage(listEvaluationFactorQuery));
    }

    @Override
    public Result<List<ListEvaluationFactorVO>> list(ListEvaluationFactorQuery listEvaluationFactorQuery) {
        return Result.success(supplierEvaluationFactorService.list(listEvaluationFactorQuery));
    }
}
