package com.tecdo.galaxy.supplier.domain.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * Date：2025/7/16 19:58
 * Description：文件信息
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class FileInfoDTO {

    /**
     * 文件url
     */
    @NotBlank(message = "文件url不能为空")
    @Schema(description = "文件url")
    private String fileUrl;

    /**
     * 文件类型
     */
    @NotBlank(message = "文件类型不能为空")
    @Pattern(regexp = "FILE_URL|FEISHU_URL", message = "文件类型只能是 FILE_URL 或 FEISHU_URL")
    @Schema(description = "文件类型", allowableValues = {"FILE_URL", "FEISHU_URL"}, example = "FILE_URL")
    private String fileType;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空")
    @Schema(description = "排序")
    @Min(value = 0, message = "排序不能小于0")
    private Integer seq;

}
