package com.tecdo.galaxy.supplier.service.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tecdo.galaxy.supplier.dao.entity.SupplierTypeEvaluationRelation;
import com.tecdo.galaxy.supplier.dao.entity.manual.SupplierTypeEvaluationRelationJoinDTO;
import com.tecdo.galaxy.supplier.dao.mapper.SupplierTypeEvaluationRelationMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * Date：2025/7/21 20:20
 * Description：供应商类型与评估要素关联关系Repository
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Repository
public class SupplierTypeEvaluationRelationRepository {

    @Resource
    private SupplierTypeEvaluationRelationMapper supplierTypeEvaluationRelationMapper;

    public IPage<SupplierTypeEvaluationRelationJoinDTO> listRelationPage(Page page, String typeName) {
        return supplierTypeEvaluationRelationMapper.listTypeEvaluationRelationJoin(page, typeName);
    }

    public int batchInsertRelations(List<SupplierTypeEvaluationRelation> list) {
        return supplierTypeEvaluationRelationMapper.batchInsert(list);
    }

    public int deleteByTypeId(Long typeId) {
        LambdaQueryWrapper<SupplierTypeEvaluationRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SupplierTypeEvaluationRelation::getTypeId, typeId);
        return supplierTypeEvaluationRelationMapper.delete(wrapper);
    }

}