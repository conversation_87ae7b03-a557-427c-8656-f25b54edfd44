package com.tecdo.galaxy.supplier.domain.req.baseinfo;


import com.tecdo.galaxy.supplier.domain.dto.PageInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Date：2025/7/7 19:21
 * Description：查询供应商信息请求体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
@Schema(description = "查询供应商信息请求体")
public class ListSupplierInfoQuery extends PageInfo {

    @Schema(description = "供应商编码", example = "GYS-232132312-dsoads")
    private String supplierCode;

    @Schema(description = "最低分数（区间查询-起始值）", example = "80.0")
    private BigDecimal minScore;

    @Schema(description = "最高分数（区间查询-结束值）", example = "100.0")
    private BigDecimal maxScore;

    @Schema(description = "评级", example = "A", allowableValues = {"A", "B", "C", "D"})
    private String rating;

    @Schema(description = "供应商类型id", example = "类型id")
    private Long typeId;

    @Schema(description = "供应商创建时间左区间", example = "2025-01-02 00:00:00")
    private LocalDateTime createTimeStart;

    @Schema(description = "供应商创建时间右区间", example = "2025-01-02 00:00:00")
    private LocalDateTime createTimeEnd;

    @Schema(description = "评级时间左区间", example = "2025-01-02 00:00:00")
    private LocalDateTime ratingUpdateTimeStart;

    @Schema(description = "评级时间右区间", example = "2025-01-02 00:00:00")
    private LocalDateTime ratingUpdateTimeEnd;

}
