package com.tecdo.galaxy.supplier.web;

import com.tecdo.galaxy.supplier.api.SupplierInfoApi;
import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.AddSupplierInfoDTO;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.ListSupplierInfoQuery;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.UpdateSupplierInfoDTO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.GetSupplierInfoVO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.ListSupplierInfoVO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.WildcardQueryNameAndCodeListByNameVO;
import com.tecdo.galaxy.supplier.service.service.SupplierInfoService;
import com.tecdo.mac.mjga.common.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Date：2025/7/7 19:28
 * Description：供应商信息
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@RestController
@Slf4j
@Validated
@RequestMapping("/supplier/info")
public class SupplierInfoController implements SupplierInfoApi {

    @Resource
    private SupplierInfoService supplierInfoService;

    @Override
    public Result<Boolean> addSupplierInfo(AddSupplierInfoDTO addSupplierInfoDTO) {
        return Result.success(supplierInfoService.addSupplierInfo(addSupplierInfoDTO));
    }

    @Override
    public Result<Boolean> updateSupplierInfo(UpdateSupplierInfoDTO updateSupplierInfoDTO) {
        return Result.success(supplierInfoService.updateSupplierInfo(updateSupplierInfoDTO));
    }

    @Override
    public Result<PageResp<ListSupplierInfoVO>> listSupplierInfoPage(ListSupplierInfoQuery listSupplierInfoQuery) {
        return Result.success(supplierInfoService.listSupplierInfo(listSupplierInfoQuery));
    }


    @Override
    public Result<GetSupplierInfoVO> getSupplierInfoDetail(long id) {
        return Result.success(supplierInfoService.getSupplierInfo(id));
    }

    @Override
    public Result<List<WildcardQueryNameAndCodeListByNameVO>> wildcardQueryNameAndCodeListByName(String name) {
        return Result.success(supplierInfoService.wildcardQueryNameAndCodeListByName(name));
    }
}
