package com.tecdo.galaxy.supplier.domain.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * Date：2025/7/27 19:08 Description：校验字段允许为空，但是不能为空字符串
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Target({FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = {NullableNotBlankValidator.class})
public @interface NullableNotBlank {

    String message() default "不能是空字符串";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
