package com.tecdo.galaxy.supplier.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tecdo.galaxy.supplier.dao.entity.SupplierFileInfo;
import com.tecdo.galaxy.supplier.dao.entity.SupplierInfo;
import com.tecdo.galaxy.supplier.domain.dto.tablequery.SupplierInfoQuery;
import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.FileInfoDTO;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.AddSupplierInfoDTO;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.ListSupplierInfoQuery;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.UpdateSupplierInfoDTO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.GetSupplierInfoVO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.ListSupplierInfoVO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.WildcardQueryNameAndCodeListByNameVO;
import com.tecdo.galaxy.supplier.service.constant.SystemConstant;
import com.tecdo.galaxy.supplier.service.enums.SupplierFileTypeEnum;
import com.tecdo.galaxy.supplier.service.repository.SupplierFileInfoRepository;
import com.tecdo.galaxy.supplier.service.repository.SupplierInfoRepository;
import com.tecdo.galaxy.supplier.service.service.SupplierInfoService;
import com.tecdo.galaxy.supplier.service.util.PageUtil;
import com.tecdo.mac.mjga.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

import static com.tecdo.galaxy.supplier.service.constant.SystemConstant.*;

/**
 * Date：2025/7/7 19:30 Description：供应商信息服务实现类
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Slf4j
@Service
public class SupplierInfoServiceImpl implements SupplierInfoService {

    /**
     * 生成供应商编号重复重试次数
     */
    private static final int MAX_RETRY_TIMES = 3;

    @Resource
    private SupplierInfoRepository supplierInfoRepository;

    @Resource
    private SupplierFileInfoRepository supplierFileInfoRepository;

    /**
     * 若并发生成供应商编号重复，会抛出DuplicateKeyException，该方法会自动重试 重试次数超过MAX_RETRY_TIMES次，抛出异常
     *
     * @param addSupplierInfoDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addSupplierInfo(AddSupplierInfoDTO addSupplierInfoDTO) {
        String creditCode = addSupplierInfoDTO.getCreditCode();
        checkCreditCodeUniqueness(creditCode);

        SupplierInfo entity = BeanUtil.copyProperties(addSupplierInfoDTO, SupplierInfo.class);
        List<SupplierFileInfo> supplierFileList =
            BeanUtil.copyToList(addSupplierInfoDTO.getFileInfoList(), SupplierFileInfo.class);
        for (int retryCount = 0; retryCount < MAX_RETRY_TIMES; retryCount++) {
            String supplierCode = generateSupplierCode();
            log.info("Generated supplier code:{}", supplierCode);
            populateFieldForEntityAndFileList(entity, supplierFileList, supplierCode);
            try {
                if (doInsert(entity, supplierFileList)) {
                    return true;
                }
            } catch (DuplicateKeyException e) {
                log.warn("Insert failed due to duplicate supplier code:{}, retrying for the {} time", supplierCode,
                    retryCount);
                continue;
            } catch (Exception e) {
                log.error("Unexpected error occurred during supplier info insertion.", e);
                throw new BizException("Insert supplier info error", e);
            }
            break;
        }
        // 超出重试次数，抛出异常
        throw new BizException("Insert supplier info error: exceeded maximum retry attempts of " + MAX_RETRY_TIMES);
    }

    /**
     * 校验统一社会信用代码的唯一性
     *
     * @param creditCode 社统码
     */
    private void checkCreditCodeUniqueness(String creditCode) {
        SupplierInfo supplierInfo = supplierInfoRepository.getByCreditCode(creditCode);
        if (Objects.nonNull(supplierInfo)) {
            throw new BizException("Credit code already exists");
        }
    }

    private void populateFieldForEntityAndFileList(SupplierInfo entity, List<SupplierFileInfo> supplierFileList,
        String supplierCode) {
        entity.setSupplierCode(supplierCode);
        entity.setStatus(SystemConstant.STATUS_VALID);
        if (CollectionUtil.isNotEmpty(supplierFileList)) {
            populateSupplierFileListBizTypeAndBizId(supplierFileList, supplierCode);
        }

    }

    private void populateSupplierFileListBizTypeAndBizId(List<SupplierFileInfo> fileList, String supplierCode) {
        fileList.forEach(file -> {
            file.setBizId(supplierCode);
            file.setBizType(SupplierFileTypeEnum.SUPPLIER_INFO_FILE.getCode());
        });
    }

    private boolean doInsert(SupplierInfo entity, List<SupplierFileInfo> supplierFileList) {
        boolean inserted = supplierInfoRepository.insert(entity) > 0;
        if (!inserted) {
            log.error("Failed to insert supplier info, supplierCode:{}", entity.getSupplierCode());
            return false;
        }
        if (CollectionUtil.isNotEmpty(supplierFileList)) {
            int insertedFiles = supplierFileInfoRepository.batchInsert(supplierFileList);
            if (insertedFiles <= 0) {
                log.error("Failed to insert file info, supplierCode: {}", entity.getSupplierCode());
            }
            return insertedFiles == supplierFileList.size();
        }
        return true;
    }

    /**
     * 生成供应商编号 生成规则：GYS-yyyy-MM-dd-0001,组成:GYS+年月日+4位数字,4四位数字按当天的序号从0递增
     *
     * @return
     */
    private String generateSupplierCode() {
        String datePart = LocalDate.now().format(DateTimeFormatter.ISO_DATE);
        SupplierInfo maxIdSupplier = supplierInfoRepository.getMaxCodeSupplierInfoByDateCodePrefix(datePart);
        int sequenceNumber = 1;
        if (maxIdSupplier != null) {
            String maxCode = maxIdSupplier.getSupplierCode();
            String sequencePart = maxCode.substring(maxCode.lastIndexOf(DASH) + 1);
            sequenceNumber = Integer.parseInt(sequencePart) + 1;
        }
        return SUPPLIER_CODE_PREFIX + datePart + DASH + String.format(SUPPLIER_CODE_SEQ_FORMAT, sequenceNumber);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateSupplierInfo(UpdateSupplierInfoDTO updateSupplierInfoDTO) {
        SupplierInfo oldEntity = supplierInfoRepository.getById(updateSupplierInfoDTO.getId());
        if (Objects.isNull(oldEntity)) {
            log.warn("SupplierInfo not found, id:{}", updateSupplierInfoDTO.getId());
            throw new BizException("SupplierInfo not found");
        }
        String supplierCode = oldEntity.getSupplierCode();
        List<SupplierFileInfo> supplierFileList =
            BeanUtil.copyToList(updateSupplierInfoDTO.getFileInfoList(), SupplierFileInfo.class);
        int batchInsertCount;
        boolean batchInsertSuccess = false;
        supplierFileInfoRepository.deleteByBizTypeAndBizId(SupplierFileTypeEnum.SUPPLIER_INFO_FILE.getCode(),
            supplierCode);
        if (CollectionUtil.isNotEmpty(supplierFileList)) {
            populateSupplierFileListBizTypeAndBizId(supplierFileList, supplierCode);
            batchInsertCount = supplierFileInfoRepository.batchInsert(supplierFileList);
            batchInsertSuccess = batchInsertCount != supplierFileList.size();
            if (batchInsertSuccess) {
                log.warn("Not all files were inserted. Expected: {}, Actual: {}", supplierFileList.size(),
                    batchInsertCount);
            }
        }

        SupplierInfo updateEntity = BeanUtil.copyProperties(updateSupplierInfoDTO, SupplierInfo.class);
        boolean updated = supplierInfoRepository.updateById(updateEntity) > 0;
        if (!updated) {
            log.warn("SupplierInfo update returned false for ID: {}", updateSupplierInfoDTO.getId());
        }
        return updated && batchInsertSuccess;
    }

    @Override
    public PageResp<ListSupplierInfoVO> listSupplierInfo(ListSupplierInfoQuery listSupplierInfoQuery) {
        SupplierInfoQuery query = BeanUtil.copyProperties(listSupplierInfoQuery, SupplierInfoQuery.class);
        IPage<SupplierInfo> pageResult = supplierInfoRepository.listPage(
            Page.of(listSupplierInfoQuery.getPageNum(), listSupplierInfoQuery.getPageSize()), query);
        log.info("querySupplierInfo end,total:{}, current:{}, totalPages:{}, pageSize:{},", pageResult.getTotal(),
            pageResult.getCurrent(), pageResult.getPages(), pageResult.getSize());
        return PageUtil.convert(pageResult, o -> transformSupplierInfo2QuerySupplierInfoListResp(o));
    }

    private ListSupplierInfoVO transformSupplierInfo2QuerySupplierInfoListResp(SupplierInfo o) {
        ListSupplierInfoVO resp = BeanUtil.copyProperties(o, ListSupplierInfoVO.class);
        resp.setCooperationYear(calculateCooperationYears(o.getCreateTime()));
        return resp;
    }

    /**
     * 根据供应商入库的 LocalDateTime 计算合作年限
     *
     * @param createDateTime 供应商入库的 LocalDateTime
     * @return 合作年限
     */
    private int calculateCooperationYears(LocalDateTime createDateTime) {
        if (createDateTime == null) {
            return 0;
        }
        // 将 LocalDateTime 转换为 LocalDate
        LocalDate createDate = createDateTime.toLocalDate();
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 计算合作年限
        return currentDate.getYear() - createDate.getYear();
    }

    @Override
    public GetSupplierInfoVO getSupplierInfo(long id) {
        SupplierInfo baseInfo = supplierInfoRepository.getById(id);
        if (Objects.isNull(baseInfo)) {
            throw new BizException("Supplier info not found");
        }
        List<SupplierFileInfo> fileInfoList =
            supplierFileInfoRepository.getByBizTypeAndBizId(SupplierFileTypeEnum.SUPPLIER_INFO_FILE.getCode(),
                baseInfo.getSupplierCode());
        GetSupplierInfoVO resp = BeanUtil.copyProperties(baseInfo, GetSupplierInfoVO.class);
        resp.setFileInfoList(BeanUtil.copyToList(fileInfoList, FileInfoDTO.class));
        return resp;
    }

    @Override
    public List<WildcardQueryNameAndCodeListByNameVO> wildcardQueryNameAndCodeListByName(String name) {
        List<SupplierInfo> supplierInfoList = supplierInfoRepository.wildcardQueryByName(name);
        return BeanUtil.copyToList(supplierInfoList, WildcardQueryNameAndCodeListByNameVO.class);
    }
}
