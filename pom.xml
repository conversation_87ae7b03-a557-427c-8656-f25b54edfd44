<?xml version="1.0" encoding="UTF-8"?><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.tecdo.galaxy</groupId>
  <artifactId>galaxy-supplier</artifactId>
  <version>${revision}</version>
  <packaging>pom</packaging>
  <name>galaxy-supplier</name>
  <parent>
    <groupId>com.tecdo.mac</groupId>
    <artifactId>mjga-parent</artifactId>
    <version>1.1.6-GA-SNAPSHOT</version>
  </parent>
  <properties>
    <revision>1.0.0-SNAPSHOT</revision>
    <mjga.verion>1.1.6-GA-SNAPSHOT</mjga.verion>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.tecdo.galaxy</groupId>
        <artifactId>galaxy-supplier-api</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.tecdo.galaxy</groupId>
        <artifactId>galaxy-supplier-client</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.tecdo.galaxy</groupId>
        <artifactId>galaxy-supplier-dao</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.tecdo.galaxy</groupId>
        <artifactId>galaxy-supplier-service</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.tecdo.galaxy</groupId>
        <artifactId>galaxy-supplier-web</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.tecdo.galaxy</groupId>
        <artifactId>galaxy-supplier-domain</artifactId>
        <version>${revision}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>com.tecdo.mac</groupId>
      <artifactId>mjga-starter-logging</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.xiaoymin</groupId>
      <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <id>nexus-public</id>
      <url>https://maven-local.tec-develop.cn/repository/maven-public/</url>
      <layout>default</layout>
      <releases/>
      <snapshots/>
    </repository>
  </repositories>
  <modules>
    <module>start</module>
    <module>galaxy-supplier-api</module>
    <module>galaxy-supplier-client</module>
    <module>galaxy-supplier-dao</module>
    <module>galaxy-supplier-service</module>
    <module>galaxy-supplier-web</module>
    <module>galaxy-supplier-domain</module>
  </modules>
</project>
