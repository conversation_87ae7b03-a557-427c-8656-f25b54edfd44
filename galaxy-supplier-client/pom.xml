<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tecdo.galaxy</groupId>
        <artifactId>galaxy-supplier</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>galaxy-supplier-client</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.tecdo.galaxy</groupId>
            <artifactId>galaxy-supplier-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tecdo.mac</groupId>
            <artifactId>mjga-starter-web-feign</artifactId>
        </dependency>
    </dependencies>
</project>
