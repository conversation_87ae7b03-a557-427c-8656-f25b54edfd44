package com.tecdo.galaxy.supplier.domain.resp.risk;

import com.tecdo.galaxy.supplier.domain.resp.baseinfo.GetSupplierInfoVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Date：2025/7/23 18:27
 * Description：风险详情响应体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class RiskDetailResp {

    @Schema(description = "基础信息")
    private GetSupplierInfoVO baseInfo;

    @Schema(description = "风险信息")
    private RiskInfo riskInfo;

    public static class RiskInfo {

    }

}


