package com.tecdo.galaxy.supplier.service.service;

import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.ListEvaluationQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.AddEvaluationDTO;
import com.tecdo.galaxy.supplier.domain.req.evaluation.ListCurrUserEvaluationRecordQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.ListHisEvaluationQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.UpdateEvaluationDTO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.ListTotalScoreEvaluationVO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.EvaluationDetailVO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.ListCurrUserEvaluationRecordVO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.ListHisEvaluationVO;

/**
 * @version V1.0.0
 * @Description: 供应商评价服务
 * @author: huan
 * @date: 2025/7/17 10:09
 * @Copyright:
 */
public interface SupplierEvaluationService {

    /**
     * 新增供应商评价
     * @param addEvaluationDTO
     * @return
     */
    boolean addEvaluation(AddEvaluationDTO addEvaluationDTO);

    /**
     * 修改供应商评价
     * @param updateEvaluationDTO
     * @return
     */
    boolean updateEvaluation(UpdateEvaluationDTO updateEvaluationDTO);

    /**
     * 查询供应商评价详情
     * @param id
     * @return
     */
    EvaluationDetailVO getEvaluationDetail(Long id);

    /**
     * 查询供应商评价列表
     * @param listEvaluationQuery
     * @return
     */
    PageResp<ListTotalScoreEvaluationVO> listTotalScoreEvaluation(ListEvaluationQuery listEvaluationQuery);

    /**
     * 查询供应商评价记录列表
     * @param listCurrUserEvaluationRecordQuery
     * @return
     */
    PageResp<ListCurrUserEvaluationRecordVO> listSupplierEvaluationRecord(
        ListCurrUserEvaluationRecordQuery listCurrUserEvaluationRecordQuery);

    /**
     * 查询供应商历史评价记录列表
     * @param listHisEvaluationQuery
     * @return
     */
    PageResp<ListHisEvaluationVO> listHisEvaluation(ListHisEvaluationQuery listHisEvaluationQuery);
}
