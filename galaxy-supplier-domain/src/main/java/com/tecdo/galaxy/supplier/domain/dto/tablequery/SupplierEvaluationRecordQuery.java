package com.tecdo.galaxy.supplier.domain.dto.tablequery;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Date：2025/7/27 12:24 Description：供应商历史评价记录通用查询条件
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class SupplierEvaluationRecordQuery {

    @Schema(description = "供应商编号")
    private String supplierCode;

    @Schema(description = "采购单验收时间")
    private LocalDateTime purchaseAcceptanceTimeStart;

    @Schema(description = "采购单验收时间")
    private LocalDateTime purchaseAcceptanceTimeEnd;

    @Schema(description = "评分状态：INIT待评分FINISH完成评分")
    private String evaluationStatus;

    @Schema(description = "评分时间左区间")
    private LocalDateTime evaluationTimeStart;

    @Schema(description = "评分时间右区间")
    private LocalDateTime evaluationTimeEnd;

    @Schema(description = "采购单号")
    private String purchaseId;

}
