package com.tecdo.galaxy.supplier.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tecdo.galaxy.supplier.dao.entity.UserEntity;
import com.tecdo.galaxy.supplier.dao.mapper.UserMapper;
import com.tecdo.galaxy.supplier.service.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, UserEntity> implements UserService {

    @Autowired
    private final UserMapper userMapper;
    @Autowired
    private final StringRedisTemplate redisTemplate;
    private static final String USER_COUNT_KEY = "USER:COUNT";


    @Override
    public long selectUserCount() {

        try {
            String unreadCount = redisTemplate.opsForValue().get(USER_COUNT_KEY);
            if (unreadCount != null) {
                return Long.parseLong(unreadCount);
            }
            // Redis中没有，从数据库获取并存入Redis
            long count = userMapper.selectUserCount();
            redisTemplate.opsForValue().set(USER_COUNT_KEY, String.valueOf(count));
            return count;
        } catch (Exception e) {
            log.error("", e);
            return 0;
        }
    }

}
