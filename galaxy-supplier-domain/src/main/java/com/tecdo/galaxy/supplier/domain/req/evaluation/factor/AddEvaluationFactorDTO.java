package com.tecdo.galaxy.supplier.domain.req.evaluation.factor;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * Date：2025/7/27 19:23 Description：新增评价要素请求体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class AddEvaluationFactorDTO {

    @NotBlank(message = "评价要素名称不能为空")
    @Schema(description = "评价要素名称")
    private String factorName;

    @NotBlank(message = "描述不能为空")
    @Schema(description = "描述")
    private String description;

    @Positive(message = "创建人非法")
    @Schema(description = "创建人")
    private Long createBy;

}
