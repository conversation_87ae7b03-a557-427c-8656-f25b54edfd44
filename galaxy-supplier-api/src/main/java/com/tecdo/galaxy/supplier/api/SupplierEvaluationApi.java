package com.tecdo.galaxy.supplier.api;

import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.ListEvaluationQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.AddEvaluationDTO;
import com.tecdo.galaxy.supplier.domain.req.evaluation.ListCurrUserEvaluationRecordQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.ListHisEvaluationQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.UpdateEvaluationDTO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.ListTotalScoreEvaluationVO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.EvaluationDetailVO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.ListCurrUserEvaluationRecordVO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.ListHisEvaluationVO;
import com.tecdo.mac.mjga.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @version V1.0.0
 * @Description:
 * @author: huan 供应商评价Api
 * @date: 2025/7/16 18:26
 * @Copyright:
 */
@Tag(name = "供应商评分相关接口", description = "供应商评分管理")
public interface SupplierEvaluationApi {

    /**
     * 新增供应商评价
     *
     * @param addEvaluationDTO
     * @return
     */
    @Operation(summary = "新增供应商评分记录")
    @PostMapping("/add")
    Result<Boolean> addEvaluation(@RequestBody @Valid AddEvaluationDTO addEvaluationDTO);

    /**
     * 修改供应商评价
     *
     * @param updateEvaluationDTO
     * @return
     */
    @Operation(summary = "修改供应商评分")
    @PostMapping("/update")
    Result<Boolean> updateEvaluation(@RequestBody @Valid UpdateEvaluationDTO updateEvaluationDTO);

    /**
     * 查询供应商评价详情
     *
     * @param id
     * @return
     */
    @Operation(summary = "查询供应商评分详情")
    @GetMapping("/detail")
    Result<EvaluationDetailVO> getEvaluationDetail(@RequestParam("id") Long id);

    /**
     * 查询供应商总评分列表
     *
     * @param listEvaluationQuery
     * @return
     */
    @Operation(summary = "查询供应商总评分列表")
    @GetMapping("/list-total-score")
    Result<PageResp<ListTotalScoreEvaluationVO>> listTotalScoreEvaluation(
        @ParameterObject ListEvaluationQuery listEvaluationQuery);

    /**
     * 查询供应商评价记录列表
     *
     * @param listCurrUserEvaluationRecordQuery
     * @return
     */
    @Operation(summary = "查询供应商评分记录列表-当前用户")
    @GetMapping("/list-curr-user")
    Result<PageResp<ListCurrUserEvaluationRecordVO>> listEvaluationRecord(
        @ParameterObject ListCurrUserEvaluationRecordQuery listCurrUserEvaluationRecordQuery);

    /**
     * 查询供应商评价记录列表
     *
     * @param listHisEvaluationQuery
     * @return
     */
    @Operation(summary = "查询供应商历史评分记录列表-所有")
    @GetMapping("/list-history")
    Result<PageResp<ListHisEvaluationVO>> listHisEvaluation(
        @ParameterObject ListHisEvaluationQuery listHisEvaluationQuery);
}
