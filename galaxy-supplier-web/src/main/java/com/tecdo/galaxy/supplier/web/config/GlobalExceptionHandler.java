package com.tecdo.galaxy.supplier.web.config;

import cn.hutool.core.util.StrUtil;
import com.tecdo.mac.mjga.common.dto.ErrorResponse;
import com.tecdo.mac.mjga.common.exception.BizException;
import com.tecdo.mac.mjga.common.exception.Code;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLException;
import java.util.stream.Collectors;

/**
 * Date：2025/7/18 16:28
 * Description：全局异常处理补充
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    private MessageSource messageSource;

    private final String CODE_DB_OPERATION_ERROR = "DBOperationError";

    @Autowired(required = false)
    public void setMessageSource(MessageSource messageSource) {

        this.messageSource = messageSource;
    }

    /**
     * MethodArgumentNotValidException 参数校验异常，将校验失败的所有异常组合成一条错误信息
     *
     * @param e 异常对象
     * @return 响应
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler({BindException.class, MethodArgumentNotValidException.class})
    public ErrorResponse bindException(BindException e) {
        log.warn("参数错误", e);
        String result = e.getFieldErrors().stream()
                .map(i -> String.format("%s(%s):%s", i.getField(), i.getRejectedValue(), i.getDefaultMessage()))
                .collect(Collectors.joining(";"));
        return ErrorResponse.build(Code.ErrParamInvalid, result);
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler({MissingServletRequestParameterException.class})
    public ErrorResponse missingServletRequestParameterException(MissingServletRequestParameterException e) {
        log.warn("参数错误", e);
        return ErrorResponse.build(Code.ErrParamInvalid, StrUtil.format("参数:{}不能为空", e.getParameterName()));
    }

    @ExceptionHandler({SQLException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ErrorResponse sqlException(SQLException e) {
        log.error("系统错误", e);
        return ErrorResponse.build(CODE_DB_OPERATION_ERROR, e.getMessage());
    }

    private String getMessage(BizException e) {
        // 没有设置国际化key直接返回Message
        if (!StringUtils.hasText(e.getI18nKey())) {
            return e.getMessage();
        }
        // 注入了spring的国际化 messageSource 对象
        if (this.messageSource != null) {
            // 如果没有配置国际化key对应的Message，则使用当前异常设置的Message
            return this.messageSource.getMessage(e.getI18nKey(), e.getParams(), e.getMessage(), LocaleContextHolder.getLocale());
        }
        // 没有注入spring的国际化 messageSource 对象
        return e.getMessage();
    }
}
