package com.tecdo.galaxy.supplier.web;

import com.tecdo.galaxy.supplier.api.SupplierEvaluationApi;
import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.ListEvaluationQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.AddEvaluationDTO;
import com.tecdo.galaxy.supplier.domain.req.evaluation.ListCurrUserEvaluationRecordQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.ListHisEvaluationQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.UpdateEvaluationDTO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.ListTotalScoreEvaluationVO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.EvaluationDetailVO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.ListCurrUserEvaluationRecordVO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.ListHisEvaluationVO;
import com.tecdo.galaxy.supplier.service.service.SupplierEvaluationService;
import com.tecdo.mac.mjga.common.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Date：2025/7/17 1:47 Description：供应商评分控制器
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@RestController
@Slf4j
@Validated
@RequestMapping("/supplier/evaluation")
public class SupplierEvaluationController implements SupplierEvaluationApi {

    @Resource
    private SupplierEvaluationService supplierEvaluationService;

    @Override
    public Result<Boolean> addEvaluation(AddEvaluationDTO addEvaluationDTO) {
        return Result.success(supplierEvaluationService.addEvaluation(addEvaluationDTO));
    }

    @Override
    public Result<Boolean> updateEvaluation(UpdateEvaluationDTO updateEvaluationDTO) {
        return Result.success(supplierEvaluationService.updateEvaluation(updateEvaluationDTO));
    }

    @Override
    public Result<EvaluationDetailVO> getEvaluationDetail(Long id) {
        return Result.success(supplierEvaluationService.getEvaluationDetail(id));
    }

    @Override
    public Result<PageResp<ListTotalScoreEvaluationVO>> listTotalScoreEvaluation(
        ListEvaluationQuery listEvaluationQuery) {
        return Result.success(supplierEvaluationService.listTotalScoreEvaluation(listEvaluationQuery));
    }

    @Override
    public Result<PageResp<ListCurrUserEvaluationRecordVO>> listEvaluationRecord(
        ListCurrUserEvaluationRecordQuery listCurrUserEvaluationRecordQuery) {
        return Result.success(
            supplierEvaluationService.listSupplierEvaluationRecord(listCurrUserEvaluationRecordQuery));
    }

    @Override
    public Result<PageResp<ListHisEvaluationVO>> listHisEvaluation(ListHisEvaluationQuery listHisEvaluationQuery) {
        return Result.success(supplierEvaluationService.listHisEvaluation(listHisEvaluationQuery));
    }
}
