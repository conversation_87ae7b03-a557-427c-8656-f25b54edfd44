package com.tecdo.galaxy.supplier.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tecdo.galaxy.supplier.dao.entity.SupplierInfo;
import com.tecdo.galaxy.supplier.domain.dto.tablequery.SupplierInfoQuery;
import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.risk.ListRiskQuery;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.GetSupplierInfoVO;
import com.tecdo.galaxy.supplier.domain.resp.risk.ListRiskVO;
import com.tecdo.galaxy.supplier.domain.resp.risk.RiskDetailResp;
import com.tecdo.galaxy.supplier.service.repository.SupplierInfoRepository;
import com.tecdo.galaxy.supplier.service.service.SupplierRiskInfoService;
import com.tecdo.galaxy.supplier.service.util.PageUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * Date：2025/7/23 21:15
 * Description：供应商风险服务实现类
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Service
public class SupplierRiskInfoServiceImpl implements SupplierRiskInfoService {

    @Resource
    private SupplierInfoRepository supplierInfoRepository;

    @Override
    public PageResp<ListRiskVO> listRisk(ListRiskQuery req) {
        SupplierInfoQuery query = BeanUtil.copyProperties(req, SupplierInfoQuery.class);
        IPage<SupplierInfo> pageResult =
            supplierInfoRepository.listPage(Page.of(req.getPageNum(), req.getPageSize()), query);
        return PageUtil.convert(pageResult, o -> BeanUtil.copyProperties(o, ListRiskVO.class));
    }

    @Override
    public RiskDetailResp detail(String supplierCode) {
        SupplierInfo baseInfo = supplierInfoRepository.getBySupplierCode(supplierCode);
        GetSupplierInfoVO baseInfoResp = BeanUtil.copyProperties(baseInfo, GetSupplierInfoVO.class);
        RiskDetailResp resp = new RiskDetailResp();
        resp.setBaseInfo(baseInfoResp);
        return resp;
    }
}
