package com.tecdo.galaxy.supplier.domain.resp.evaluation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Date：2025/7/16 21:04
 * Description：获取供应商评价记录详情响应体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class EvaluationDetailVO {

    /**
     * 基础评分信息
     */
    @Schema(description = "基础评分信息")
    private EvaluationBaseInfo baseInfo;

    /**
     * 历史评分信息
     */
    @Schema(description = "历史评分信息")
    private List<ListHisEvaluationVO> hisEvaluationInfoList;

    /**
     * 评价基础信息
     */
    @Data
    public static class EvaluationBaseInfo {

        /**
         * 交付速度分
         */
        @Schema(description = "交付速度分")
        private BigDecimal deliverySpeedScore;

        /**
         * 质量合格率
         */
        @Schema(description = "质量合格率")
        private BigDecimal qualityPassScore;

        /**
         * 服务响应速度得分
         */
        @Schema(description = "服务响应速度得分")
        private BigDecimal serviceResponseScore;

        /**
         * 价格竞争力得分
         */
        @Schema(description = "价格竞争力得分")
        private BigDecimal priceScore;

        /**
         * 总分
         */
        @Schema(description = "总分")
        private BigDecimal score;
    }
}



