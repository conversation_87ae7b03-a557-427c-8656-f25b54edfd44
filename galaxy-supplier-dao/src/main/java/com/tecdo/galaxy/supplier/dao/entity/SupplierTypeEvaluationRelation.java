package com.tecdo.galaxy.supplier.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 供应商类型-评价要素关联关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Getter
@Setter
@ToString
@TableName("supplier_type_evaluation_relation")
@Schema(name = "SupplierTypeEvaluationRelation", description = "供应商类型-评价要素关联关系表")
public class SupplierTypeEvaluationRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 供应商类型id
     */
    @TableField("type_id")
    @Schema(description = "供应商类型id")
    private Long typeId;

    /**
     * 供应商评价要素id
     */
    @TableField("factor_id")
    @Schema(description = "供应商评价要素id")
    private Long factorId;

    /**
     * 权重（小于100，步长为5）
     */
    @TableField("weight")
    @Schema(description = "权重（小于100，步长为5）")
    private Integer weight;

    /**
     * 创建人id
     */
    @TableField("create_by")
    @Schema(description = "创建人id")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人id
     */
    @TableField("update_by")
    @Schema(description = "更新人id")
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    @Schema(description = "租户id")
    private String tenantId;
}
