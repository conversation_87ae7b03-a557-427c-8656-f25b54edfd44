package com.tecdo.galaxy.supplier.domain.req.evaluation;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Date：2025/7/16 18:36
 * Description：新增供应商评价请求体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class AddEvaluationDTO {

    /**
     * 采购单号
     */
    @NotBlank(message = "采购单号不能为空")
    @Schema(description = "采购单号")
    private String purchaseId;

    /**
     * 采购计划单号
     */
    @NotBlank(message = "采购计划单号不能为空")
    @Schema(description = "采购计划单号")
    private String purchasePlanId;

    /**
     * 供应商编号
     */
    @NotBlank(message = "供应商编号不能为空")
    @Schema(description = "供应商编号")
    private String supplierCode;

    /**
     * 采购单验收时间
     */
    @NotNull(message = "采购单验收时间不能为空")
    @Schema(description = "采购单验收时间")
    private LocalDateTime purchaseAcceptanceTime;

    @Positive(message = "创建人非法")
    @Schema(description = "创建人")
    private Long createBy;
}
