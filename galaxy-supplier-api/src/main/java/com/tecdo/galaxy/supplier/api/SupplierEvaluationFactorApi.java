package com.tecdo.galaxy.supplier.api;

import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.evaluation.factor.AddEvaluationFactorDTO;
import com.tecdo.galaxy.supplier.domain.req.evaluation.factor.ListEvaluationFactorQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.factor.UpdateEvaluationFactorDTO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.factor.ListEvaluationFactorVO;
import com.tecdo.mac.mjga.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @version V1.0.0
 * @Description: 供应商评价要素接口
 * @author: huan
 * @date: 2025/7/21 16:36
 * @Copyright:
 */
@Tag(name = "供应商评分要素相关接口", description = "供应商评分要素管理")
public interface SupplierEvaluationFactorApi {

    /**
     * 新增供应商评分要素
     *
     * @param addEvaluationFactorDTO
     * @return
     */
    @Operation(summary = "新增供应商评分要素")
    @PostMapping("/add")
    Result<Boolean> add(@RequestBody @Valid AddEvaluationFactorDTO addEvaluationFactorDTO);

    /**
     * 修改供应商评分要素
     *
     * @param updateEvaluationFactorDTO
     * @return
     */
    @Operation(summary = "修改供应商评分要素")
    @PostMapping("/update")
    Result<Boolean> update(@RequestBody @Valid UpdateEvaluationFactorDTO updateEvaluationFactorDTO);

    /**
     * 查询供应商评价要素列表
     *
     * @param listEvaluationFactorQuery
     * @return
     */
    @GetMapping("/list-page")
    @Operation(summary = "查询供应商评价要素列表-分页")
    Result<PageResp<ListEvaluationFactorVO>> listPage(
        @ParameterObject ListEvaluationFactorQuery listEvaluationFactorQuery);

    /**
     * 查询供应商评价要素列表
     *
     * @param listEvaluationFactorQuery
     * @return
     */
    @GetMapping("/list")
    @Operation(summary = "查询供应商评价要素列表")
    Result<List<ListEvaluationFactorVO>> list(@ParameterObject ListEvaluationFactorQuery listEvaluationFactorQuery);
}
