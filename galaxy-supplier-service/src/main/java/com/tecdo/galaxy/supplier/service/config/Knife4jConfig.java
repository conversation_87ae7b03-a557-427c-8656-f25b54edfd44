package com.tecdo.galaxy.supplier.service.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Date：2025/7/6 0:55
 * Description：Knife4j配置
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Configuration
public class Knife4jConfig implements WebMvcConfigurer {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Galaxy管理后台接口文档")
                        .version("v1.0.0")
                        .description("Galaxy管理后台的接口文档描述"));
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 只写资源路径，不带context-path，Spring会自动拼接
        registry.addResourceHandler("/doc.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }
}
