package com.tecdo.galaxy.supplier.api;

import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.risk.ListRiskQuery;
import com.tecdo.galaxy.supplier.domain.resp.risk.ListRiskVO;
import com.tecdo.galaxy.supplier.domain.resp.risk.RiskDetailResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @version V1.0.0
 * @Description: 供应商风险api
 * @author: huan
 * @date: 2025/7/23 18:09
 * @Copyright:
 */
@Tag(name = "供应商风险相关接口")
public interface SupplierRiskApi {

    /**
     * 查询供应商风险列表-分页
     *
     * @param listRiskQuery
     * @return
     */
    @Operation(summary = "查询供应商评级列表")
    @GetMapping("/list-page")
    PageResp<ListRiskVO> listRiskPage(@ParameterObject ListRiskQuery listRiskQuery);

    /**
     * 查询供应商风险详情
     * @param supplierCode
     * @return
     */
    @Operation(summary = "查询供应商风险详情")
    @GetMapping("/detail")
    RiskDetailResp detail(@RequestParam("supplierCode") String supplierCode);
}
