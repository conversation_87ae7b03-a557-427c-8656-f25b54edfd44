package com.tecdo.galaxy.supplier.domain.resp.baseinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Date：2025/7/16 21:02
 * Description：查询供应商评分列表响应体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class ListTotalScoreEvaluationVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "供应商编号(规则:GYS-yy-mm-dd-0001)")
    private String supplierCode;

    @Schema(description = "供应商名称")
    private String name;

    @Schema(description = "供应商类型")
    private String supplierType;

    @Schema(description = "交付速度分")
    private BigDecimal deliverySpeedScore;

    @Schema(description = "质量合格率")
    private BigDecimal qualityPassScore;

    @Schema(description = "服务响应速度得分")
    private BigDecimal serviceResponseScore;

    @Schema(description = "价格竞争力得分")
    private BigDecimal priceScore;

    @Schema(description = "总分")
    private BigDecimal score;

    @Schema(description = "当前评级(A/B/C/D)")
    private String currentRating;

}
