package com.tecdo.galaxy.supplier.domain.req.baseinfo;

import com.tecdo.galaxy.supplier.domain.page.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Date：2025/7/16 21:03
 * Description：查询供应商评分请求体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class ListEvaluationQuery extends PageReq {

    @Schema(description = "供应商编码", example = "GYS-232132312-dsoads")
    private String supplierCode;

    @Schema(description = "最低分数（区间查询-起始值）", example = "3.0")
    private BigDecimal minScore;

    @Schema(description = "最高分数（区间查询-结束值）", example = "5.0")
    private BigDecimal maxScore;

    @Schema(description = "供应商类型", example = "日化洗护")
    private String supplierType;
}
