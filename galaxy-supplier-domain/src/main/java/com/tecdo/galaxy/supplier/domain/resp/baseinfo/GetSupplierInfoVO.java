package com.tecdo.galaxy.supplier.domain.resp.baseinfo;

import com.tecdo.galaxy.supplier.domain.req.FileInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Date：2025/7/7 18:15
 * Description：查询供应商信息返回体
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
@Schema(description = "查询供应商信息返回体")
public class GetSupplierInfoVO {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "供应商编号(规则:GYS-yy-mm-dd-0001)", example = "GYS-25-07-09-0001")
    private String supplierCode;

    @Schema(description = "供应商名称", example = "北京科技有限公司")
    private String name;

    @Schema(description = "统一社会信用代码", example = "91110000123456789X")
    private String creditCode;

    @Schema(description = "注册地址", example = "北京市朝阳区xxx街道xxx号")
    private String registerAddress;

    @Schema(description = "法定代表人", example = "张三")
    private String legalPerson;

    @Schema(description = "主营业务", example = "软件开发、技术咨询")
    private String mainBusiness;

    @Schema(description = "注册资本(万元)", example = "1000.00")
    private BigDecimal registeredCapital;

    @Schema(description = "营业开始日期", example = "2020-01-01")
    private LocalDate businessStartDate;

    @Schema(description = "营业期限", example = "长期")
    private String businessTerm;

    @Schema(description = "供应商类型", example = "生产型")
    private String supplierType;

    @Schema(description = "供应商联系人姓名", example = "李四")
    private String contactName;

    @Schema(description = "供应商联系人电话", example = "***********")
    private String contactPhone;

    @Schema(description = "供应商联系人邮箱", example = "<EMAIL>")
    private String contactEmail;

    @Schema(description = "开户行", example = "中国工商银行北京分行")
    private String bankName;

    @Schema(description = "银行账号", example = "6222021234567890123")
    private String bankAccountNo;

    @Schema(description = "账户户名", example = "北京科技有限公司")
    private String bankAccountName;

    @Schema(description = "合作次数", example = "5")
    private Integer cooperationCount;

    @Schema(description = "风险预警次数", example = "0")
    private Integer riskCount;

    @Schema(description = "最近评分", example = "95.5")
    private BigDecimal score;

    @Schema(description = "当前评级(A/B/C/D)", example = "A")
    private String currentRating;

    @Schema(description = "状态(VALID:正常,INVALID:失效)", example = "VALID")
    private String status;

    @Schema(description = "创建人", example = "admin")
    private Long createBy;

    @Schema(description = "创建时间", example = "2025-07-09T10:30:00")
    private LocalDateTime createTime;

    @Schema(description = "文件信息")
    private List<FileInfoDTO> fileInfoList;

}
