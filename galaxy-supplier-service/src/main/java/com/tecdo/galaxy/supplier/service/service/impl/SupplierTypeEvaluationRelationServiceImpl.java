package com.tecdo.galaxy.supplier.service.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tecdo.galaxy.supplier.dao.entity.SupplierTypeEvaluationRelation;
import com.tecdo.galaxy.supplier.dao.entity.manual.SupplierTypeEvaluationRelationJoinDTO;
import com.tecdo.galaxy.supplier.domain.page.PageResp;
import com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation.AddRelationDTO;
import com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation.FactorRatioInfo;
import com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation.ListTypeEvaluationRelationQuery;
import com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation.UpdateRelationDTO;
import com.tecdo.galaxy.supplier.domain.resp.evaluation.tyepefactorrelation.ListTypeEvaluationRelationVO;
import com.tecdo.galaxy.supplier.service.repository.SupplierTypeEvaluationRelationRepository;
import com.tecdo.galaxy.supplier.service.service.SupplierTypeEvaluationRelationService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Date：2025/7/21 20:19 Description：供应商类型-评价要素关联关系服务实现类
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Service
public class SupplierTypeEvaluationRelationServiceImpl implements SupplierTypeEvaluationRelationService {

    @Resource
    private SupplierTypeEvaluationRelationRepository supplierTypeEvaluationRelationRepository;

    @Override
    public PageResp<ListTypeEvaluationRelationVO> listPage(
        ListTypeEvaluationRelationQuery listTypeEvaluationRelationQuery) {
        Page<SupplierTypeEvaluationRelationJoinDTO> page =
            Page.of(listTypeEvaluationRelationQuery.getPageNum(), listTypeEvaluationRelationQuery.getPageSize());
        // 执行关联查询
        IPage<SupplierTypeEvaluationRelationJoinDTO> joinPageResult =
            supplierTypeEvaluationRelationRepository.listRelationPage(page,
                listTypeEvaluationRelationQuery.getTypeName());

        // 处理查询结果，按供应商类型分组
        List<ListTypeEvaluationRelationVO> result = processJoinPageResult(joinPageResult.getRecords());
        return PageResp.of(listTypeEvaluationRelationQuery.getPageNum(), listTypeEvaluationRelationQuery.getPageSize(),
            joinPageResult.getTotal(), result);

    }

    @Override
    public boolean addRelation(AddRelationDTO addRelationDTO) {
        List<SupplierTypeEvaluationRelation> relationList =
            convertToRelationList(addRelationDTO.getTypeId(), addRelationDTO.getFactorRatioInfoList(),
                addRelationDTO.getOperator());
        return supplierTypeEvaluationRelationRepository.batchInsertRelations(relationList) == relationList.size();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateRelation(UpdateRelationDTO updateRelationDTO) {
        List<SupplierTypeEvaluationRelation> relationList =
            convertToRelationList(updateRelationDTO.getTypeId(), updateRelationDTO.getFactorRatioInfoList(),
                updateRelationDTO.getOperator());
        //先删除后插入
        supplierTypeEvaluationRelationRepository.deleteByTypeId(updateRelationDTO.getTypeId());
        return supplierTypeEvaluationRelationRepository.batchInsertRelations(relationList) == relationList.size();
    }

    /**
     * 将类型 ID、要素比例信息列表和操作人转换为供应商类型评价关系列表
     *
     * @param typeId              供应商类型 ID
     * @param factorRatioInfoList 要素比例信息列表
     * @param operator            操作人
     * @return 供应商类型评价关系列表
     */
    private List<SupplierTypeEvaluationRelation> convertToRelationList(Long typeId,
        List<FactorRatioInfo> factorRatioInfoList, Long operator) {
        return factorRatioInfoList.stream().map(factorRatioInfo -> {
            SupplierTypeEvaluationRelation relation = new SupplierTypeEvaluationRelation();
            relation.setTypeId(typeId);
            relation.setFactorId(factorRatioInfo.getFactorId());
            relation.setWeight(factorRatioInfo.getWeight());
            relation.setCreateBy(operator);
            return relation;
        }).collect(Collectors.toList());
    }

    /**
     * 处理查询结果并按供应商类型分组
     */
    private List<ListTypeEvaluationRelationVO> processJoinPageResult(
        List<SupplierTypeEvaluationRelationJoinDTO> records) {
        // 实现分组处理逻辑
        return records.stream().collect(Collectors.groupingBy(SupplierTypeEvaluationRelationJoinDTO::getTypeName))
            .entrySet().stream().map(this::createResponseFromGroup).collect(Collectors.toList());
    }

    private ListTypeEvaluationRelationVO createResponseFromGroup(
        Map.Entry<String, List<SupplierTypeEvaluationRelationJoinDTO>> entry) {
        List<SupplierTypeEvaluationRelationJoinDTO> group = entry.getValue();
        ListTypeEvaluationRelationVO resp = new ListTypeEvaluationRelationVO();
        resp.setTypeName(entry.getKey());
        resp.setFactorDesc(buildDesc(group));
        resp.setUpdateTime(group.get(0).getUpdateTime());
        return resp;
    }

    /**
     * 用,拼接评价要素描述
     *
     * @param group
     * @return
     */
    private String buildDesc(List<SupplierTypeEvaluationRelationJoinDTO> group) {
        String desc = CollectionUtil.join(group.stream().map(this::buildDesc).filter(Objects::nonNull).toList(), ", ");
        return StrUtil.emptyToNull(desc);
    }

    /**
     * 拼接样式
     *
     * @param dto
     * @return
     */
    private String buildDesc(SupplierTypeEvaluationRelationJoinDTO dto) {
        if (StrUtil.isAllNotBlank(dto.getFactorName(), dto.getWeight())) {
            return StrUtil.format("{}({}%)", dto.getFactorName(), dto.getWeight());
        }
        return null;
    }
}
