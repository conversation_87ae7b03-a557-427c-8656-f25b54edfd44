package com.tecdo.galaxy.supplier.domain.req.evaluation.tyepefactorrelation;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * Date：2025/7/27 20:30 Description：添加供应商类型评价要素关联关系
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Data
public class UpdateRelationDTO {

    @NotNull(message = "关联关系id不能为空")
    @Schema(description = "关联关系id", example = "1")
    private Long id;

    @Schema(description = "供应商类型id", example = "1")
    private Long typeId;

    @NotEmpty(message = "评价要素列表不能为空")
    @Size(min = 1, message = "评价要素不低于1个")
    @Valid
    @Schema(description = "评价要素列表")
    private List<FactorRatioInfo> factorRatioInfoList;

    @Positive(message = "操作人非法")
    @Schema(description = "操作人")
    private Long operator;

}