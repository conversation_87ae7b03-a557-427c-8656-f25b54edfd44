package com.tecdo.galaxy.supplier.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.tecdo.galaxy.supplier.dao.entity.SupplierType;
import com.tecdo.galaxy.supplier.domain.dto.tablequery.SupplierTypeQuery;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.AddTypeDTO;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.BatchManagerSupplierTypeDTO;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.ListSupplierTypeQuery;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.UpdateTypeDTO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.type.SupplierTypeTreeVO;
import com.tecdo.galaxy.supplier.service.repository.SupplierInfoRepository;
import com.tecdo.galaxy.supplier.service.repository.SupplierTypeRepository;
import com.tecdo.galaxy.supplier.service.service.SupplierTypeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Date：2025/7/21 17:54 Description：供应商类型服务实现
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Service
public class SupplierTypeServiceImpl implements SupplierTypeService {

    @Resource
    private SupplierTypeRepository supplierTypeRepository;

    @Resource
    private SupplierInfoRepository supplierInfoRepository;

    @Override
    public boolean addType(AddTypeDTO addTypeDTO) {
        return supplierTypeRepository.insert(BeanUtil.copyProperties(addTypeDTO, SupplierType.class));
    }

    @Override
    public boolean updateType(UpdateTypeDTO updateTypeDTO) {
        SupplierType supplierType = new SupplierType();
        supplierType.setId(updateTypeDTO.getId());
        supplierType.setName(updateTypeDTO.getName());
        supplierType.setDescription(updateTypeDTO.getDescription());
        supplierType.setUpdateBy(Long.valueOf(updateTypeDTO.getUpdateBy()));
        supplierType.setUpdateTime(LocalDateTime.now());
        return supplierTypeRepository.updateById(supplierType);
    }

    @Override
    public List<SupplierTypeTreeVO> listTree(ListSupplierTypeQuery listSupplierTypeQuery) {
        // 查询所有符合条件的数据
        SupplierTypeQuery query = new SupplierTypeQuery();
        query.setName(listSupplierTypeQuery.getName());
        List<SupplierType> supplierTypes = supplierTypeRepository.list(query);

        // 构建ID到节点的映射
        Map<Long, SupplierTypeTreeVO> idToNodeMap = supplierTypes.stream()
                .map(this::convertToTreeVO)
                .collect(Collectors.toMap(SupplierTypeTreeVO::getId, vo -> vo));

        // 构建树形结构
        List<SupplierTypeTreeVO> rootNodes = new ArrayList<>();
        for (SupplierType type : supplierTypes) {
            SupplierTypeTreeVO vo = idToNodeMap.get(type.getId());
            if (type.getParentId() == null || type.getParentId() == 0) {
                // 根节点
                rootNodes.add(vo);
            } else {
                // 子节点，找到父节点并添加到其children中
                SupplierTypeTreeVO parentVO = idToNodeMap.get(type.getParentId());
                if (parentVO != null) {
                    if (parentVO.getChildren() == null) {
                        parentVO.setChildren(new ArrayList<>());
                    }
                    parentVO.getChildren().add(vo);
                }
            }
        }

        return rootNodes;
    }

    /**
     * 将SupplierType转换为SupplierTypeTreeVO
     * @param supplierType
     * @return
     */
    private SupplierTypeTreeVO convertToTreeVO(SupplierType supplierType) {
        SupplierTypeTreeVO vo = new SupplierTypeTreeVO();
        vo.setId(supplierType.getId());
        vo.setParentId(supplierType.getParentId());
        vo.setName(supplierType.getName());
        vo.setDescription(supplierType.getDescription());
        vo.setLevel(supplierType.getLevel());
        vo.setPath(supplierType.getPath());
        return vo;
    }

    @Override
    public boolean batchManagerSupplierType(BatchManagerSupplierTypeDTO batchManagerSupplierTypeDTO) {
        return supplierInfoRepository.batchUpdateTypeIdBySupplierCodeList(
            batchManagerSupplierTypeDTO.getSupplierCodeList(), batchManagerSupplierTypeDTO.getTypeId(),
            batchManagerSupplierTypeDTO.getOperator()) > 0;
    }
}
