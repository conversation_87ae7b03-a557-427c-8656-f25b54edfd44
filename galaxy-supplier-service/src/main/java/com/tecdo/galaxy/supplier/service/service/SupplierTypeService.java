package com.tecdo.galaxy.supplier.service.service;

import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.AddTypeDTO;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.BatchManagerSupplierTypeDTO;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.ListSupplierTypeQuery;
import com.tecdo.galaxy.supplier.domain.req.baseinfo.type.UpdateTypeDTO;
import com.tecdo.galaxy.supplier.domain.resp.baseinfo.type.SupplierTypeTreeVO;

import java.util.List;

/**
 * Date：2025/7/31 18:30
 * Description：供应商类型服务接口
 *
 * <AUTHOR>
 * @version v1.0.0
 */
public interface SupplierTypeService {

    /**
     * 新增供应商类型
     *
     * @param addTypeDTO 新增供应商类型请求参数
     * @return 是否新增成功
     */
    boolean addType(AddTypeDTO addTypeDTO);

    /**
     * 更新供应商类型
     *
     * @param updateTypeDTO 更新供应商类型请求参数
     * @return 是否更新成功
     */
    boolean updateType(UpdateTypeDTO updateTypeDTO);

    /**
     * 查询供应商类型树形结构
     *
     * @param listSupplierTypeQuery 查询参数
     * @return 供应商类型树形结构
     */
    List<SupplierTypeTreeVO> listTree(ListSupplierTypeQuery listSupplierTypeQuery);

    /**
     * 批量管理供应商类型
     *
     * @param batchManagerSupplierTypeDTO 批量管理供应商类型请求参数
     * @return 是否操作成功
     */
    boolean batchManagerSupplierType(BatchManagerSupplierTypeDTO batchManagerSupplierTypeDTO);
}